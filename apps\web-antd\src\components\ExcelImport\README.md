# Excel导入组件

一个通用的Excel/CSV文件导入组件，支持文件上传、数据预览、验证和导入功能。

## 功能特性

- 📁 支持CSV文件导入
- 📊 完整的Excel文件支持（.xlsx、.xls）
- 🔍 数据预览和验证
- 📋 多工作表支持（Excel）
- ✅ 自定义数据验证
- 🔄 数据转换功能
- 📈 导入进度显示
- 🎨 美观的步骤式界面

## 依赖说明

组件已内置xlsx库支持，无需额外安装依赖。

## 基本用法

```vue
<template>
  <div>
    <Button @click="openImport">导入数据</Button>
    <ExcelImport
      ref="excelImportRef"
      :max-size="5"
      :validate-data="validateImportData"
      :transform-data="transformImportData"
      @success="handleImportSuccess"
      @error="handleImportError"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Button } from 'ant-design-vue';
import { ExcelImport } from '#/components';
import type { ValidationResult } from '#/components';

const excelImportRef = ref();

// 打开导入弹窗
const openImport = () => {
  excelImportRef.value?.open();
};

// 数据验证函数
const validateImportData = (data: any[]): ValidationResult => {
  const errors: string[] = [];

  data.forEach((row, index) => {
    if (!row.name) {
      errors.push(`第${index + 1}行：姓名不能为空`);
    }
    if (!row.email) {
      errors.push(`第${index + 1}行：邮箱不能为空`);
    }
  });

  return {
    valid: errors.length === 0,
    errors,
  };
};

// 数据转换函数
const transformImportData = (data: any[]) => {
  return data.map((row, index) => ({
    id: `import_${Date.now()}_${index}`,
    name: row[0], // 第一列作为姓名
    email: row[1], // 第二列作为邮箱
    phone: row[2], // 第三列作为电话
    createTime: new Date().toISOString(),
  }));
};

// 导入成功回调
const handleImportSuccess = (data: any[]) => {
  console.log('导入成功:', data);
  // 这里可以将数据添加到表格或发送到后端
};

// 导入失败回调
const handleImportError = (error: string) => {
  console.error('导入失败:', error);
};
</script>
```

## 高级用法

### 自定义列配置

```vue
<template>
  <ExcelImport :columns="customColumns" @success="handleImportSuccess" />
</template>

<script setup lang="ts">
const customColumns = [
  { title: '姓名', dataIndex: 'name', required: true },
  { title: '邮箱', dataIndex: 'email', required: true },
  { title: '电话', dataIndex: 'phone', required: false },
];
</script>
```

### 与表格组件结合

```vue
<template>
  <div>
    <div class="mb-4">
      <Button type="primary" @click="openImport"> 导入数据 </Button>
    </div>

    <Table
      :columns="tableColumns"
      :data-source="tableData"
      :pagination="{ pageSize: 10 }"
    />

    <ExcelImport ref="excelImportRef" @success="handleImportSuccess" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const tableData = ref([]);
const excelImportRef = ref();

const tableColumns = [
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '邮箱', dataIndex: 'email', key: 'email' },
  { title: '电话', dataIndex: 'phone', key: 'phone' },
];

const openImport = () => {
  excelImportRef.value?.open();
};

const handleImportSuccess = (data: any[]) => {
  // 将导入的数据添加到表格
  tableData.value = [...tableData.value, ...data];
};
</script>
```

## API

### Props

| 参数          | 说明             | 类型     | 默认值            |
| ------------- | ---------------- | -------- | ----------------- |
| accept        | 允许的文件类型   | string   | '.csv,.xlsx,.xls' |
| maxSize       | 最大文件大小(MB) | number   | 10                |
| columns       | 表格列配置       | array    | []                |
| validateData  | 数据验证函数     | function | -                 |
| transformData | 数据转换函数     | function | -                 |

### Events

| 事件名  | 说明     | 回调参数                |
| ------- | -------- | ----------------------- |
| success | 导入成功 | (data: any[]) => void   |
| error   | 导入失败 | (error: string) => void |

### Methods

| 方法名 | 说明         | 参数 |
| ------ | ------------ | ---- |
| open   | 打开导入弹窗 | -    |
| close  | 关闭导入弹窗 | -    |

## 文件格式要求

### CSV文件格式

- 使用UTF-8编码
- 第一行为表头
- 使用逗号分隔字段
- 字段值如包含逗号，请用双引号包围

示例CSV文件：

```csv
姓名,邮箱,电话
张三,<EMAIL>,13800138000
李四,<EMAIL>,13900139000
"王五,经理",<EMAIL>,13700137000
```

### Excel文件格式

- 支持.xlsx和.xls格式
- 支持多工作表，可以在导入时切换不同的工作表
- 第一行建议作为表头
- 支持各种数据类型（文本、数字、日期等）

### 多工作表支持

当Excel文件包含多个工作表时：

- 组件会自动检测所有工作表
- 在数据预览步骤显示工作表选择器
- 用户可以点击不同的工作表按钮切换数据
- 每个工作表的数据独立预览和验证

## 注意事项

1. 组件已完整支持CSV和Excel文件格式
2. 大文件导入可能会影响浏览器性能，建议限制文件大小
3. 数据验证和转换函数应该处理异常情况
4. 组件使用Vben Modal，确保项目中已正确配置
5. Excel文件支持多工作表切换，CSV文件只有一个工作表
6. 导入弹窗默认全屏显示，提供更好的数据预览体验
