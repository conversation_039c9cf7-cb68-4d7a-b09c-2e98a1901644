<template>
  <div class="edittable-import-example">
    <PageWrapper title="EditTable 导入功能示例">
      <template #content>
        <div class="mb-4">
          <Alert
            message="EditTable 导入功能演示"
            description="这个示例展示了如何使用 EditTable 组件的导入功能，支持基于表格列配置的数据导入和模板下载。"
            type="info"
            show-icon
            closable
          />
        </div>

        <!-- 产品信息表格 -->
        <Card title="产品信息管理" class="mb-6">
          <EditTable
            ref="editTableRef"
            v-model="tableData"
            :params="tableParams"
            @initialized="handleTableInitialized"
          />
        </Card>

        <!-- 用户信息表格 -->
        <Card title="用户信息管理" class="mb-6">
          <EditTable
            ref="userTableRef"
            v-model="userData"
            :params="userTableParams"
            @initialized="handleUserTableInitialized"
          />
        </Card>
      </template>
    </PageWrapper>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

import { Alert, Card } from 'ant-design-vue';

import { EditTable } from '#/components';
import { PageWrapper } from '#/layouts';

// 产品信息表格数据
const tableData = ref([
  {
    id: 1,
    name: '苹果手机',
    category: '电子产品',
    price: 5999,
    stock: 100,
    status: 1,
    createTime: '2024-01-01',
  },
  {
    id: 2,
    name: '华为笔记本',
    category: '电子产品',
    price: 7999,
    stock: 50,
    status: 1,
    createTime: '2024-01-02',
  },
]);

// 产品信息表格配置
const tableParams = ref({
  // 表格列配置
  columns: [
    {
      field: 'id',
      title: 'ID',
      width: 80,
      type: 'number',
      align: 'center',
      fixed: 'left',
    },
    {
      field: 'name',
      title: '产品名称',
      width: 150,
      type: 'text',
      required: true,
    },
    {
      field: 'category',
      title: '产品分类',
      width: 120,
      type: 'text',
      required: true,
    },
    {
      field: 'price',
      title: '价格',
      width: 100,
      type: 'number',
      required: true,
    },
    {
      field: 'stock',
      title: '库存',
      width: 80,
      type: 'number',
    },
    {
      field: 'status',
      title: '状态',
      width: 100,
      type: 'text_tag',
      options: [
        { label: '启用', value: 1, color: 'green' },
        { label: '禁用', value: 0, color: 'red' },
      ],
    },
    {
      field: 'createTime',
      title: '创建时间',
      width: 160,
      type: 'date',
    },
  ],
  // 表单配置（用于新增/编辑）
  form: [
    {
      field: 'name',
      title: '产品名称',
      component: 'Input',
      required: true,
    },
    {
      field: 'category',
      title: '产品分类',
      component: 'Input',
      required: true,
    },
    {
      field: 'price',
      title: '价格',
      component: 'InputNumber',
      required: true,
    },
    {
      field: 'stock',
      title: '库存',
      component: 'InputNumber',
    },
    {
      field: 'status',
      title: '状态',
      component: 'Select',
      config: {
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
    },
  ],
  // 工具栏按钮配置（支持下拉菜单）
  tablist: [
    {
      title: '新增',
      type: 'add',
      key: 'add',
      icon: 'add',
    },
    {
      title: '明细文件',
      type: 'import',
      key: 'import_dropdown',
      icon: 'import',
      children: [
        {
          title: '导入明细',
          type: 'import',
          key: 'import',
          icon: 'import',
        },
        {
          title: '下载模板',
          type: 'download_template',
          key: 'download_template',
          icon: 'download_template',
        },
      ],
    },
  ],
});

// 用户信息表格数据
const userData = ref([
  {
    id: 1,
    name: '张三',
    email: '<EMAIL>',
    phone: '13800138001',
    department: '技术部',
    position: '前端工程师',
    status: 1,
    createTime: '2024-01-01',
  },
]);

// 用户信息表格配置
const userTableParams = ref({
  columns: [
    {
      field: 'id',
      title: 'ID',
      width: 80,
      type: 'number',
      align: 'center',
      fixed: 'left',
    },
    {
      field: 'name',
      title: '姓名',
      width: 120,
      type: 'text',
      required: true,
    },
    {
      field: 'email',
      title: '邮箱',
      width: 200,
      type: 'email',
      required: true,
    },
    {
      field: 'phone',
      title: '电话',
      width: 150,
      type: 'phone',
    },
    {
      field: 'department',
      title: '部门',
      width: 120,
      type: 'text',
    },
    {
      field: 'position',
      title: '职位',
      width: 120,
      type: 'text',
    },
    {
      field: 'status',
      title: '状态',
      width: 100,
      type: 'text_tag',
      options: [
        { label: '在职', value: 1, color: 'green' },
        { label: '离职', value: 0, color: 'red' },
      ],
    },
    {
      field: 'createTime',
      title: '创建时间',
      width: 160,
      type: 'date',
    },
  ],
  form: [
    {
      field: 'name',
      title: '姓名',
      component: 'Input',
      required: true,
    },
    {
      field: 'email',
      title: '邮箱',
      component: 'Input',
      required: true,
    },
    {
      field: 'phone',
      title: '电话',
      component: 'Input',
    },
    {
      field: 'department',
      title: '部门',
      component: 'Input',
    },
    {
      field: 'position',
      title: '职位',
      component: 'Input',
    },
  ],
  tablist: [
    {
      title: '新增',
      type: 'add',
      key: 'add',
      icon: 'add',
    },
    {
      title: '明细文件',
      type: 'import',
      key: 'import_dropdown',
      icon: 'import',
      children: [
        {
          title: '导入明细',
          type: 'import',
          key: 'import',
          icon: 'import',
        },
        {
          title: '下载模板',
          type: 'download_template',
          key: 'download_template',
          icon: 'download_template',
        },
      ],
    },
  ],
});

// 表格引用
const editTableRef = ref();
const userTableRef = ref();

// 表格初始化完成回调
const handleTableInitialized = () => {
  console.log('产品信息表格初始化完成');
};

const handleUserTableInitialized = () => {
  console.log('用户信息表格初始化完成');
};
</script>

<style scoped>
.edittable-import-example {
  padding: 16px;
}
</style>
