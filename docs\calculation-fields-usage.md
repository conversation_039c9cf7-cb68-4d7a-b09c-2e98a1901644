# 计算字段使用指南

## 概述

计算字段是一种特殊的表单字段类型，它的值不是由用户直接输入，而是根据表格数据进行自动计算得出。当表格数据发生变化时，计算字段会自动重新计算并更新显示值。

## 快速开始

### 1. 基础配置

在后端返回的表单配置中，添加计算字段：

```javascript
{
  field: 'total_amount',
  title: '总金额',
  type: 'calculated',
  config: {
    calculation: {
      type: 'sum',                    // 计算类型
      sourceFields: ['amount'],       // 依赖的表格字段
      precision: 2,                   // 小数位数
      defaultValue: 0,                // 默认值
    },
  },
}
```

### 2. 在Vue组件中使用

```vue
<template>
  <VbenForm
    ref="formRef"
    :schema="formSchema"
    @values-change="handleValuesChange"
  />
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useFormCalculationIntegration } from '#/utils/calculation/form-calculation-integration';

const formRef = ref();
let calculationIntegration = null;

onMounted(async () => {
  await nextTick();

  if (formRef.value?.formApi) {
    // 创建计算字段集成器
    calculationIntegration = useFormCalculationIntegration(
      formRef.value.formApi,
    ).integration;

    // 自动扫描并注册计算字段
    calculationIntegration.scanAndRegisterCalculationFields(formSchema.value);
  }
});

// 表单值变化时触发重新计算
const handleValuesChange = (values, changedFields) => {
  if (changedFields.includes('table_field_name')) {
    calculationIntegration?.recalculate(values.table_field_name);
  }
};
</script>
```

## 支持的计算类型

### 1. 求和 (sum)

```javascript
{
  type: 'sum',
  sourceFields: ['price'],
  precision: 2,
}
```

### 2. 平均值 (average)

```javascript
{
  type: 'average',
  sourceFields: ['score'],
  precision: 1,
}
```

### 3. 乘积 (product)

```javascript
{
  type: 'product',
  sourceFields: ['length', 'width', 'height'],
  precision: 3,
}
```

### 4. 最大值 (max)

```javascript
{
  type: 'max',
  sourceFields: ['price'],
  precision: 2,
}
```

### 5. 最小值 (min)

```javascript
{
  type: 'min',
  sourceFields: ['price'],
  precision: 2,
}
```

### 6. 计数 (count)

```javascript
{
  type: 'count',
  sourceFields: ['id'],  // 任意字段都可以用于计数
}
```

### 7. 自定义公式 (formula)

```javascript
{
  type: 'formula',
  sourceFields: ['revenue', 'cost'],
  formula: '(field1_sum - field2_sum) / field1_sum * 100',
  precision: 2,
}
```

## 过滤条件

可以对表格数据进行过滤，只对满足条件的数据进行计算：

```javascript
{
  type: 'count',
  sourceFields: ['user_id'],
  filter: [
    {
      field: 'status',
      operator: '=',
      value: 'active',
    },
    {
      field: 'score',
      operator: '>',
      value: 60,
    },
  ],
}
```

### 支持的过滤操作符

- `=` - 等于
- `!=` - 不等于
- `>` - 大于
- `<` - 小于
- `>=` - 大于等于
- `<=` - 小于等于
- `in` - 包含于数组
- `not_in` - 不包含于数组

## 自定义公式语法

在自定义公式中，可以使用以下变量：

- `field1_sum`, `field2_sum` - 各字段的求和值
- `field1_avg`, `field2_avg` - 各字段的平均值
- `field1_count`, `field2_count` - 各字段的计数
- `field1_max`, `field2_max` - 各字段的最大值
- `field1_min`, `field2_min` - 各字段的最小值

支持的运算符：`+`, `-`, `*`, `/`, `()`

## 完整示例

```javascript
// 订单表单配置
const orderFormConfig = [
  // 订单明细表格
  {
    field: 'order_items',
    title: '订单明细',
    type: 'edittable',
    config: {
      columns: [
        { field: 'product_name', title: '商品名称' },
        { field: 'quantity', title: '数量' },
        { field: 'unit_price', title: '单价' },
        { field: 'amount', title: '小计' },
      ],
    },
  },

  // 计算字段：订单总金额
  {
    field: 'total_amount',
    title: '订单总金额',
    type: 'calculated',
    config: {
      calculation: {
        type: 'sum',
        sourceFields: ['amount'],
        precision: 2,
        defaultValue: 0,
      },
    },
  },

  // 计算字段：商品总数量
  {
    field: 'total_quantity',
    title: '商品总数量',
    type: 'calculated',
    config: {
      calculation: {
        type: 'sum',
        sourceFields: ['quantity'],
        precision: 0,
        defaultValue: 0,
      },
    },
  },

  // 计算字段：平均单价
  {
    field: 'average_price',
    title: '平均单价',
    type: 'calculated',
    config: {
      calculation: {
        type: 'average',
        sourceFields: ['unit_price'],
        precision: 2,
        defaultValue: 0,
      },
    },
  },
];
```

## 注意事项

1. **字段依赖**：确保 `sourceFields` 中指定的字段在表格数据中存在
2. **数据类型**：计算字段会自动将非数字值转换为0进行计算
3. **性能优化**：系统默认启用防抖功能，避免频繁计算
4. **错误处理**：计算出错时会使用默认值，并在控制台输出警告信息
5. **内存管理**：组件卸载时会自动清理计算字段和监听器
6. **精度处理**：系统已解决JavaScript浮点数精度问题，确保计算结果准确

## 精度问题解决方案

### JavaScript浮点数精度问题

JavaScript中的浮点数运算存在精度问题，例如：

```javascript
console.log(0.1 + 0.2); // 输出: 0.30000000000000004 而不是 0.3
console.log(0.1 * 3); // 输出: 0.30000000000000004 而不是 0.3
```

### 我们的解决方案

计算引擎使用以下技术解决精度问题：

1. **精确的数字处理**：使用 `Number.EPSILON` 和精确的舍入算法
2. **安全的数学运算**：实现了 `safeAdd` 和 `safeMultiply` 方法
3. **智能精度控制**：根据配置的 `precision` 参数精确控制小数位数

### 精度配置示例

```javascript
// 财务计算：保留2位小数
{
  type: 'sum',
  sourceFields: ['amount'],
  precision: 2,  // 确保结果如 123.45 而不是 123.44999999999999
}

// 统计计算：保留4位小数
{
  type: 'average',
  sourceFields: ['score'],
  precision: 4,  // 适用于需要高精度的统计计算
}

// 整数计算：不保留小数
{
  type: 'count',
  sourceFields: ['id'],
  precision: 0,  // 计数结果为整数
}
```

### 测试验证

系统包含完整的精度测试用例，确保：

- 小数加法运算精确
- 小数乘法运算精确
- 复杂公式计算精确
- 边界情况处理正确

## 调试

可以通过以下方式获取调试信息：

```javascript
// 获取已注册的计算字段
const registeredFields = calculationIntegration.getRegisteredFields();

// 获取详细调试信息
const debugInfo = calculationIntegration.getDebugInfo();

console.log('计算字段调试信息:', debugInfo);
```

## 常见问题

### Q: 计算字段不更新怎么办？

A: 检查以下几点：

1. 确保表格数据字段名与 `sourceFields` 配置一致
2. 确认表格数据变化时调用了 `recalculate` 方法
3. 检查控制台是否有错误信息

### Q: 如何手动触发重新计算？

A: 调用 `calculationIntegration.recalculate(tableData)` 方法

### Q: 可以在一个表单中使用多个计算字段吗？

A: 可以，系统支持同时使用多个计算字段，它们会独立计算

### Q: 计算字段支持嵌套依赖吗？

A: 目前不支持计算字段之间的依赖关系，只支持依赖表格数据字段
