<template>
  <div class="excel-import-demo">
    <Card title="Excel导入组件演示" class="mb-4">
      <div class="demo-actions mb-4">
        <Button type="primary" @click="openImport" class="mr-2">
          导入数据
        </Button>
        <Button @click="clearData" :disabled="tableData.length === 0">
          清空数据
        </Button>
      </div>

      <div class="demo-stats mb-4">
        <Alert
          :message="`当前共有 ${tableData.length} 条数据`"
          type="info"
          show-icon
        />
      </div>

      <Table
        :columns="tableColumns"
        :data-source="tableData"
        :pagination="{ pageSize: 10, showSizeChanger: true }"
        size="small"
        bordered
      >
        <template #empty>
          <div class="empty-state">
            <InboxOutlined style="font-size: 48px; color: #ccc" />
            <p>暂无数据，请点击"导入数据"按钮导入</p>
          </div>
        </template>
      </Table>
    </Card>

    <!-- Excel导入组件 -->
    <ExcelImport
      ref="excelImportRef"
      :max-size="5"
      :validate-data="validateImportData"
      :transform-data="transformImportData"
      @success="handleImportSuccess"
      @error="handleImportError"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

import { Alert, Button, Card, Table, message } from 'ant-design-vue';
import { InboxOutlined } from '@ant-design/icons-vue';

import { ExcelImport } from '#/components';
import type { ValidationResult } from '#/components';

// 表格数据
const tableData = ref<any[]>([]);
const excelImportRef = ref();

// 表格列配置
const tableColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 80,
    align: 'center',
  },
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    width: 120,
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
    width: 200,
  },
  {
    title: '电话',
    dataIndex: 'phone',
    key: 'phone',
    width: 150,
  },
  {
    title: '部门',
    dataIndex: 'department',
    key: 'department',
    width: 120,
  },
  {
    title: '导入时间',
    dataIndex: 'importTime',
    key: 'importTime',
    width: 180,
  },
];

// 打开导入弹窗
const openImport = () => {
  excelImportRef.value?.open();
};

// 清空数据
const clearData = () => {
  tableData.value = [];
  message.success('数据已清空');
};

// 数据验证函数
const validateImportData = (data: any[]): ValidationResult => {
  const errors: string[] = [];
  
  if (data.length === 0) {
    errors.push('没有可导入的数据');
    return { valid: false, errors };
  }

  data.forEach((row, index) => {
    const rowNum = index + 1;
    
    // 检查必填字段
    if (!row[0] || String(row[0]).trim() === '') {
      errors.push(`第${rowNum}行：姓名不能为空`);
    }
    
    if (!row[1] || String(row[1]).trim() === '') {
      errors.push(`第${rowNum}行：邮箱不能为空`);
    } else {
      // 简单的邮箱格式验证
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(String(row[1]).trim())) {
        errors.push(`第${rowNum}行：邮箱格式不正确`);
      }
    }
    
    // 检查电话格式（如果有值的话）
    if (row[2] && String(row[2]).trim()) {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(String(row[2]).trim().replace(/\D/g, ''))) {
        errors.push(`第${rowNum}行：电话格式不正确`);
      }
    }
  });
  
  return {
    valid: errors.length === 0,
    errors,
  };
};

// 数据转换函数
const transformImportData = (data: any[]) => {
  return data.map((row, index) => ({
    key: `import_${Date.now()}_${index}`,
    index: tableData.value.length + index + 1,
    name: String(row[0] || '').trim(),
    email: String(row[1] || '').trim(),
    phone: String(row[2] || '').trim(),
    department: String(row[3] || '').trim() || '未分配',
    importTime: new Date().toLocaleString('zh-CN'),
  }));
};

// 导入成功回调
const handleImportSuccess = (data: any[]) => {
  console.log('导入成功:', data);
  
  // 将数据添加到表格
  tableData.value = [...tableData.value, ...data];
  
  message.success(`成功导入 ${data.length} 条数据`);
};

// 导入失败回调
const handleImportError = (error: string) => {
  console.error('导入失败:', error);
  message.error(`导入失败: ${error}`);
};
</script>

<style scoped>
.excel-import-demo {
  padding: 24px;
}

.demo-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.demo-stats {
  margin: 16px 0;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.empty-state p {
  margin-top: 16px;
  font-size: 14px;
}

/* 表格样式优化 */
:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}
</style>
