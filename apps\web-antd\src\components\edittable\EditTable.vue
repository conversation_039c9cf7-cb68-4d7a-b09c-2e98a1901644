<!--
  可编辑表格组件 (EditTable)

  功能特性：
  - 支持新增、编辑、删除、复制行数据
  - 集成文件上传功能，自动处理Upload字段数据格式
  - 支持分页显示
  - 防抖操作，避免重复提交
  - 自动数据同步和表格更新

  主要方法：
  - handleAdd: 新增数据
  - handleEdit: 编辑数据
  - handleCopy: 复制数据
  - handleDelete: 删除数据
  - handleModalConfirm: 处理表单提交

  数据流：
  props.modelValue/params.tabList → tableData → currentPageData → Grid显示
-->
<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { ToolsButton } from '#/components';
import { calculationManager } from '#/utils/calculation/calculation-manager';
import { transformColumns } from '#/utils/Columns/GridColumns';
import { transformBackendSearchToSchema } from '#/utils/search-schema';
import { normalizeUploadFieldsInFormConfig } from '#/utils/upload/normalizeFileObject';

import commomModal from '../common-modal/index.vue';

/**
 * 组件Props接口定义
 */
interface Props {
  /**
   * 组件配置参数
   */
  params?: {
    [key: string]: any;
    /** 表格列配置 */
    columns?: any[];
    /** 表单字段配置（用于schema转换） */
    form?: any[];
    /** 表单配置（用于新增/编辑弹窗） */
    formProps?: any;
    /** 表格属性配置 */
    tableProps?: any;
    /** 表格数据列表 */
    tabList?: any[];
  };
  /**
   * v-model双向绑定的数据数组
   * 支持外部直接控制表格数据
   */
  modelValue?: any[];
}

const props = withDefaults(defineProps<Props>(), {
  params: () => ({}),
  modelValue: () => [],
});

// 定义 emit 事件
const emit = defineEmits(['update:modelValue', 'initialized']);

// ==================== 数据处理工具函数 ====================

// 用于确保唯一性的计数器
let keyCounter = 0;

// 生成唯一键的函数
const generateUniqueKey = () => {
  keyCounter++;
  return `row_${Date.now()}_${keyCounter}_${Math.random().toString(36).slice(2, 11)}`;
};

// 检查数据中是否有重复的 key
const validateUniqueKeys = (data: any[], context = 'unknown') => {
  const keyMap = new Map<string, number>();
  const duplicates: string[] = [];

  data.forEach((item, index) => {
    const key = item._customKey;
    if (key !== undefined && key !== null && key !== '') {
      if (keyMap.has(key)) {
        duplicates.push(
          `Key "${key}" found at indices ${keyMap.get(key)} and ${index}`,
        );
      } else {
        keyMap.set(key, index);
      }
    }
  });

  if (duplicates.length > 0) {
    console.warn(`[EditTable ${context}] Duplicate keys detected:`, duplicates);
    console.warn('Data with duplicate keys:', data);
  }

  return duplicates.length === 0;
};

// 防抖操作状态管理
const operationStates = ref({
  copying: false,
  deleting: false,
  modalConfirm: false,
});

// 通用防抖函数
const withDebounce = async (
  operationType: keyof typeof operationStates.value,
  operation: () => Promise<void> | void,
  delay: number = 300,
) => {
  if (operationStates.value[operationType]) {
    return;
  }

  try {
    operationStates.value[operationType] = true;
    await operation();
  } finally {
    setTimeout(() => {
      operationStates.value[operationType] = false;
    }, delay);
  }
};

/**
 * 初始化表格数据，为每行数据添加唯一标识符
 * @param data 原始数据数组
 * @returns 处理后的数据数组，每项都包含 _customKey 字段
 */
const initializeTableData = (data: any[]) => {
  // 数据安全检查：确保 data 是数组，如果不是则使用空数组
  const safeData = Array.isArray(data) ? data : [];

  if (!Array.isArray(data)) {
    console.warn('initializeTableData - 接收到非数组数据，已转换为空数组');
  }

  // 为每行数据添加唯一标识符
  // 优先级：原有_customKey > 原有id > 生成新的唯一键
  const usedKeys = new Set<string>();
  const processedData = safeData.map((item, index) => {
    let customKey;

    // 优先使用已存在的 _customKey
    if (
      item._customKey !== undefined &&
      item._customKey !== null &&
      item._customKey !== '' &&
      !usedKeys.has(String(item._customKey))
    ) {
      customKey = String(item._customKey);
    }
    // 其次使用 id，但要确保 id 是有效值（不是 false、0、null、undefined、空字符串）
    else if (
      item.id !== undefined &&
      item.id !== null &&
      item.id !== '' &&
      item.id !== false &&
      !usedKeys.has(String(item.id))
    ) {
      customKey = String(item.id); // 转换为字符串确保一致性
    }
    // 最后生成新的唯一键
    else {
      do {
        customKey = generateUniqueKey();
      } while (usedKeys.has(customKey)); // 确保生成的 key 是唯一的
    }

    // 记录已使用的 key
    usedKeys.add(customKey);

    return {
      ...item,
      _customKey: customKey,
      // 添加索引信息用于调试（可选）
      _originalIndex: index,
    };
  });

  // 验证是否有重复的 key
  validateUniqueKeys(processedData, 'initializeTableData');

  return processedData;
};

/**
 * 复制行数据时的数据处理函数
 * 清除不应该被复制的字段，如ID、时间戳等
 * @param originalRow 原始行数据
 * @returns 处理后的复制数据
 */
const getCopyRowData = (originalRow: any) => {
  // 复制原始数据并重置关键字段
  const copiedData = {
    ...originalRow,
    _customKey: generateUniqueKey(), // 生成新的唯一键
    id: undefined, // 清除原始ID，让后端生成新ID
    _X_ROW_KEY: undefined, // 清除VXE表格的行键
  };

  // 清除时间相关字段（这些应该由后端自动生成）
  const timeFields = [
    'created_at',
    'updated_at', // 标准时间字段
    'createTime',
    'updateTime', // 驼峰命名时间字段
    'createdAt',
    'updatedAt', // 另一种驼峰命名
  ];

  timeFields.forEach((field) => {
    delete copiedData[field];
  });

  return copiedData;
};

/**
 * 在数据数组中查找目标行的索引
 * 使用多种策略进行匹配，确保能够准确找到目标行
 * @param targetRow 目标行数据
 * @param dataArray 数据数组
 * @returns 目标行在数组中的索引，未找到返回-1
 */
const findRowIndex = (targetRow: any, dataArray: any[]) => {
  return dataArray.findIndex((item: any) => {
    // 策略1：优先使用自定义键进行匹配（最可靠）
    if (
      targetRow._customKey !== undefined &&
      targetRow._customKey !== null &&
      targetRow._customKey !== '' &&
      item._customKey !== undefined &&
      item._customKey !== null &&
      item._customKey !== ''
    ) {
      return item._customKey === targetRow._customKey;
    }

    // 策略2：使用数据库ID进行比较（次优选择）
    // 确保 id 是有效值（不是 false、0、null、undefined、空字符串）
    if (
      targetRow.id !== undefined &&
      targetRow.id !== null &&
      targetRow.id !== '' &&
      targetRow.id !== false &&
      item.id !== undefined &&
      item.id !== null &&
      item.id !== '' &&
      item.id !== false
    ) {
      return item.id === targetRow.id;
    }

    // 策略3：使用VXE Table的行键进行比较（表格内部标识）
    if (
      targetRow._X_ROW_KEY !== undefined &&
      targetRow._X_ROW_KEY !== null &&
      targetRow._X_ROW_KEY !== '' &&
      item._X_ROW_KEY !== undefined &&
      item._X_ROW_KEY !== null &&
      item._X_ROW_KEY !== ''
    ) {
      return item._X_ROW_KEY === targetRow._X_ROW_KEY;
    }

    // 策略4：对象引用比较（最后的备选方案）
    return item === targetRow;
  });
};

// ==================== 响应式数据 ====================

// 表格数据（优先使用 v-model 的值）
const tableData = ref<any[]>(
  initializeTableData(
    props.modelValue?.length ? props.modelValue : props.params?.tabList || [],
  ),
);

// 初始化数据完成

// 防止循环更新的标志位
const isInternalUpdate = ref(false);

// 分页配置
const paginationConfig = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

// 计算当前页显示的数据
const currentPageData = computed(() => {
  const { currentPage, pageSize } = paginationConfig.value;
  const start = (currentPage - 1) * pageSize;
  const end = start + pageSize;
  return tableData.value.slice(start, end);
});

// Modal 实例
const [Modal, modalApi] = useVbenModal({
  connectedComponent: commomModal,
});

// Excel导入组件引用
const excelImportRef = ref();

// ==================== 计算属性 ====================

// 转换列配置
const gridColumns = computed(() => {
  if (!props?.params?.columns) return [];

  // 检查是否有重复的 field
  const columns = props?.params?.columns || [];
  const fieldMap = new Map<string, boolean>();
  const duplicateFields: string[] = [];

  columns.forEach((col: any) => {
    const field = col.field || col.dataIndex;
    if (field) {
      if (fieldMap.has(field)) {
        duplicateFields.push(field);
      } else {
        fieldMap.set(field, true);
      }
    }
  });

  // 检测到重复字段时的处理已完成

  // 使用去重后的 transformColumns
  const transformedColumns = transformColumns(props?.params?.columns);

  // 为 actions 类型的列添加点击处理函数
  transformedColumns.forEach((column: any) => {
    if (column.cellRender?.name === 'CellOperation') {
      column.cellRender.attrs = {
        ...column.cellRender.attrs,
        onClick: (params: { code: string; row: any }) => {
          // 构造按钮对象
          const button = {
            type: params.code,
            ...params,
          };

          // 调用页面级的按钮点击处理函数
          // 参数格式：(buttonKey, buttonCode, row, button)
          handleButtonClick(undefined, params.code, params.row, button);
        },
      };
    }
  });

  return transformedColumns;
});

// ==================== 表格初始化 ====================

// 分页事件处理（只在分页切换时使用 loading）
const handlePageChange = (params: {
  currentPage: number;
  pageSize: number;
}) => {
  // 分页切换时显示 loading
  _gridApi.setLoading(true);

  // 更新分页配置
  paginationConfig.value.currentPage = params.currentPage;
  paginationConfig.value.pageSize = params.pageSize;

  // 使用 setTimeout 模拟异步，让用户看到 loading 效果
  setTimeout(() => {
    // 更新表格显示
    updateGridData();
    _gridApi.setLoading(false);
  }, 100);
};

// 创建表格实例
const [Grid, _gridApi] = useVbenVxeGrid({
  gridOptions: {
    maxHeight: '700px',
    columns: [],
    data: [],
    // 配置行键，使用自定义键
    rowConfig: {
      useKey: true,
      keyField: '_customKey', // 使用自定义键作为行标识
    },
    pagerConfig: {
      enabled: true,
    },
    ...props.params?.tableProps,
  },
  gridEvents: {
    pageChange: handlePageChange,
  },
});

// ==================== 生命周期和监听器 ====================

// 更新分页配置
const updatePaginationConfig = () => {
  paginationConfig.value.total = tableData.value.length;
  // 如果当前页超出范围，重置到第一页
  const maxPage =
    Math.ceil(tableData.value.length / paginationConfig.value.pageSize) || 1;
  if (paginationConfig.value.currentPage > maxPage) {
    paginationConfig.value.currentPage = 1;
  }
};

// 数据同步到父组件（v-model）
const syncDataToParent = () => {
  // 设置内部更新标志，防止循环
  isInternalUpdate.value = true;

  // 移除内部使用的 _customKey，只保留业务数据
  const cleanData = tableData.value.map((item) => {
    const { _customKey, _X_ROW_KEY, ...businessData } = item;
    return businessData;
  });

  emit('update:modelValue', cleanData);

  // 触发计算字段重新计算
  calculationManager.recalculateAllFields(cleanData);

  // 延迟重置标志位
  nextTick(() => {
    isInternalUpdate.value = false;
  });
};

// 不触发同步的表格更新函数
const updateGridDataWithoutSync = () => {
  // 更新分页配置
  updatePaginationConfig();

  // 安全地更新表格配置，避免循环引用
  _gridApi.setGridOptions({
    columns: gridColumns.value,
    data: [...currentPageData.value], // 使用展开运算符避免响应式对象直接传递
    pagerConfig: {
      enabled: true,
      currentPage: paginationConfig.value.currentPage,
      pageSize: paginationConfig.value.pageSize,
      total: paginationConfig.value.total,
    },
  });
};

// 统一的表格更新函数
const updateGridData = () => {
  // 先更新表格
  updateGridDataWithoutSync();

  // 再同步数据到父组件
  syncDataToParent();
};

// 组件挂载后初始化表格
onMounted(() => {
  // 初始化时不触发同步，避免循环
  updateGridDataWithoutSync();

  // 延迟触发初始化完成事件，确保表格完全渲染
  nextTick(() => {
    setTimeout(() => {
      emit('initialized');
    }, 100);
  });
});

// 优化的数据监听器（只监听数据长度变化，避免深度监听）
watch(
  () => tableData.value.length,
  (newLength, oldLength) => {
    if (newLength !== oldLength) {
      // 只有在非内部更新时才触发同步
      if (isInternalUpdate.value) {
        updateGridDataWithoutSync();
      } else {
        updateGridData();
      }
    }
  },
);

// 监听列配置变化（避免深度监听）
watch(
  () => gridColumns.value.length,
  () => {
    updateGridData();
  },
);

// 监听 v-model 数据变化
watch(
  () => props.modelValue,
  (newValue) => {
    // 如果是内部更新触发的，忽略此次变化
    if (isInternalUpdate.value) {
      return;
    }

    if (newValue && Array.isArray(newValue)) {
      tableData.value = initializeTableData(newValue);
      // 验证处理后的数据
      validateUniqueKeys(tableData.value, 'modelValue watch');
      // 注意：这里不调用 updateGridData，避免再次触发同步
      updateGridDataWithoutSync();
    }
  },
  { deep: true },
);

// ==================== 按钮操作处理 ====================

// 解析按钮点击参数
const parseButtonArgs = (args: any[]) => {
  if (args.length === 4) {
    const [buttonKey, buttonCode, row, button] = args;
    return { buttonKey, buttonCode, row, button };
  } else if (args.length === 3 && typeof args[1] === 'string') {
    const [buttonKey, buttonCode, button] = args;
    return { buttonKey, buttonCode, row: null, button };
  }
  throw new Error(`未知的参数格式: ${args.length} 个参数`);
};

/**
 * 处理新增操作
 * 打开新增弹窗
 */
const handleAdd = () => {
  // 转换后端表单配置为前端schema，传递新增模式
  const schema = transformBackendSearchToSchema(props.params?.form || [], {
    formMode: 'add',
  });

  modalApi
    .setState({ title: '新增数据' })
    .setData({
      schema,
      isEdit: false, // 明确标记这是新增操作
    })
    .open();
};

// 复制操作
const handleCopy = (row: any) => {
  withDebounce('copying', async () => {
    const copiedData = getCopyRowData(row);

    // 添加到本地数据源
    tableData.value.push(copiedData);

    // 验证数据完整性
    validateUniqueKeys(tableData.value, 'handleCopy');

    // 统一更新表格
    updateGridData();

    message.success('复制成功');
  }).catch(() => {
    message.error('复制失败，请重试');
  });
};

// 删除操作
const handleDelete = (row: any) => {
  withDebounce('deleting', async () => {
    const index = findRowIndex(row, tableData.value);

    if (index === -1) {
      message.error('未找到要删除的数据');
      return;
    }

    // 从本地数据源删除
    tableData.value.splice(index, 1);

    // 统一更新表格
    updateGridData();

    message.success('删除成功');
  }).catch(() => {
    message.error('删除失败，请重试');
  });
};

/**
 * 处理编辑操作
 * 打开编辑弹窗并预填充表单数据
 * @param row 要编辑的行数据
 */
const handleEdit = (row: any) => {
  // 转换后端表单配置为前端schema，传递编辑模式
  const schema = transformBackendSearchToSchema(props.params?.form || [], {
    formMode: 'edit',
  });

  // 处理表单数据，确保Upload字段的值是数组格式
  const processedFormData = { ...row };

  // 使用工具函数标准化Upload类型字段的数据格式
  if (props.params?.form && Array.isArray(props.params.form)) {
    normalizeUploadFieldsInFormConfig(
      processedFormData,
      props.params.form,
      'edittable',
    );
  }

  // 打开编辑弹窗
  modalApi
    .setState({ title: '编辑数据' })
    .setData({
      schema, // 表单配置
      formData: processedFormData, // 预填充的表单数据
      originalRow: row, // 保存原始行数据用于后续更新识别
      isEdit: true, // 标记为编辑操作
    })
    .open();
};

/**
 * 处理Excel导入操作
 * 打开Excel导入弹窗
 */
const handleImport = () => {
  excelImportRef.value?.open();
};

/**
 * 处理下载模板操作
 */
const handleDownloadTemplate = () => {
  const templateData = generateImportTemplate();

  if (templateData.headers.length === 0) {
    message.warning('没有可导入的列配置');
    return;
  }

  try {
    // 动态导入 XLSX 库
    import('xlsx')
      .then((XLSX) => {
        // 创建工作簿
        const workbook = XLSX.utils.book_new();
        const worksheetData = [templateData.headers, ...templateData.data];
        const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

        // 设置列宽
        const colWidths = templateData.headers.map(() => ({ wch: 15 }));
        worksheet['!cols'] = colWidths;

        // 添加工作表
        XLSX.utils.book_append_sheet(workbook, worksheet, '导入模板');

        // 下载文件
        const fileName = `导入模板_${new Date().toISOString().slice(0, 10)}.xlsx`;
        XLSX.writeFile(workbook, fileName);

        message.success('模板下载成功');
      })
      .catch((error) => {
        console.error('XLSX库加载失败:', error);
        message.error('模板下载失败，请重试');
      });
  } catch (error) {
    console.error('模板下载失败:', error);
    message.error('模板下载失败，请重试');
  }
};

/**
 * Excel导入数据验证函数
 * 根据表格列配置或表单配置验证导入的数据
 */
const validateImportData = (data: any[]) => {
  const errors: string[] = [];

  if (data.length === 0) {
    errors.push('没有可导入的数据');
    return { valid: false, errors };
  }

  // 优先使用表格列配置，如果没有则使用表单配置
  const columnsConfig = props.params?.columns || [];
  const formConfig = props.params?.form || [];

  // 如果有表格列配置，使用表格列配置进行验证
  if (columnsConfig.length > 0) {
    // 过滤出可导入的列（排除操作列等特殊列）
    const importableColumns = columnsConfig.filter(
      (col: any) =>
        col.field &&
        col.type !== 'action' &&
        col.type !== 'actions' &&
        col.field !== 'action' &&
        col.field !== 'actions',
    );

    data.forEach((row, index) => {
      const rowNum = index + 1;

      // 检查必填字段（基于列配置的required属性）
      importableColumns.forEach((column: any, columnIndex: number) => {
        if (
          column.required &&
          (!row[columnIndex] || String(row[columnIndex]).trim() === '')
        ) {
          errors.push(
            `第${rowNum}行第${columnIndex + 1}列（${column.title || column.field}）不能为空`,
          );
        }
      });
    });
  } else if (formConfig.length > 0) {
    // 如果没有表格列配置，回退到表单配置验证
    data.forEach((row, index) => {
      const rowNum = index + 1;

      // 检查必填字段
      formConfig.forEach((field: any, fieldIndex: number) => {
        if (
          field.required &&
          (!row[fieldIndex] || String(row[fieldIndex]).trim() === '')
        ) {
          errors.push(
            `第${rowNum}行第${fieldIndex + 1}列（${field.title || field.field}）不能为空`,
          );
        }
      });
    });
  }

  return {
    valid: errors.length === 0,
    errors,
  };
};

/**
 * Excel导入数据转换函数
 * 将导入的数组数据转换为对象格式
 * 优先使用表格列配置，如果没有则使用表单配置
 */
const transformImportData = (data: any[]) => {
  // 优先使用表格列配置，如果没有则使用表单配置
  const columnsConfig = props.params?.columns || [];
  const formConfig = props.params?.form || [];

  return data.map((row, _index) => {
    const transformedRow: any = {
      _customKey: generateUniqueKey(),
    };

    // 如果有表格列配置，使用表格列配置进行字段映射
    if (columnsConfig.length > 0) {
      // 过滤出可导入的列（排除操作列等特殊列）
      const importableColumns = columnsConfig.filter(
        (col: any) =>
          col.field &&
          col.type !== 'action' &&
          col.type !== 'actions' &&
          col.field !== 'action' &&
          col.field !== 'actions',
      );

      // 根据表格列配置映射字段
      importableColumns.forEach((column: any, columnIndex: number) => {
        const fieldName = column.field;
        if (fieldName && row[columnIndex] !== undefined) {
          transformedRow[fieldName] = row[columnIndex];
        }
      });
    } else if (formConfig.length > 0) {
      // 如果没有表格列配置，回退到表单配置映射
      formConfig.forEach((field: any, fieldIndex: number) => {
        const fieldName = field.field || field.fieldName;
        if (fieldName && row[fieldIndex] !== undefined) {
          transformedRow[fieldName] = row[fieldIndex];
        }
      });
    }

    return transformedRow;
  });
};

/**
 * Excel导入成功回调
 */
const handleImportSuccess = (data: any[]) => {
  // 将导入的数据添加到表格
  tableData.value = [...tableData.value, ...data];

  // 验证数据完整性
  validateUniqueKeys(tableData.value, 'handleImportSuccess');

  // 更新表格显示
  updateGridData();

  message.success(`成功导入 ${data.length} 条数据`);
};

/**
 * Excel导入失败回调
 */
const handleImportError = (error: string) => {
  message.error(`导入失败: ${error}`);
};

/**
 * 生成导入模板
 * 基于表格列配置生成Excel导入模板
 */
const generateImportTemplate = () => {
  // 优先使用表格列配置，如果没有则使用表单配置
  const columnsConfig = props.params?.columns || [];
  const formConfig = props.params?.form || [];

  let templateHeaders: string[] = [];
  let templateData: any[] = [];

  if (columnsConfig.length > 0) {
    // 基于表格列配置生成模板
    const importableColumns = columnsConfig.filter(
      (col: any) =>
        col.field &&
        col.type !== 'action' &&
        col.type !== 'actions' &&
        col.field !== 'action' &&
        col.field !== 'actions',
    );

    templateHeaders = importableColumns.map(
      (col: any) => col.title || col.field,
    );

    // 生成示例数据行（可选）
    const exampleRow = importableColumns.map((col: any) => {
      switch (col.type) {
        case 'date': {
          return '2024-01-01';
        }
        case 'email': {
          return '<EMAIL>';
        }
        case 'number': {
          return '100';
        }
        case 'phone': {
          return '13800138000';
        }
        case 'text': {
          return `示例${col.title || col.field}`;
        }
        default: {
          return `示例${col.title || col.field}`;
        }
      }
    });
    templateData = [exampleRow];
  } else if (formConfig.length > 0) {
    // 基于表单配置生成模板
    templateHeaders = formConfig.map(
      (field: any) => field.title || field.field || field.fieldName,
    );

    // 生成示例数据行
    const exampleRow = formConfig.map((field: any) => {
      switch (field.component) {
        case 'DatePicker': {
          return '2024-01-01';
        }
        case 'Input': {
          return `示例${field.title || field.field}`;
        }
        case 'InputNumber': {
          return '100';
        }
        default: {
          return `示例${field.title || field.field}`;
        }
      }
    });
    templateData = [exampleRow];
  }

  return {
    headers: templateHeaders,
    data: templateData,
  };
};

// 页面级别的按钮点击处理函数
function handleButtonClick(...args: any[]) {
  try {
    const { buttonCode, row } = parseButtonArgs(args);

    switch (buttonCode) {
      case 'add': {
        handleAdd();
        break;
      }
      case 'copy_row': {
        handleCopy(row);
        break;
      }
      case 'delete': {
        handleDelete(row);
        break;
      }
      case 'detail': {
        // 详情查看功能待实现
        break;
      }
      case 'download_template': {
        handleDownloadTemplate();
        break;
      }
      case 'edit': {
        handleEdit(row);
        break;
      }
      case 'import': {
        handleImport();
        break;
      }
      default: {
        // 未处理的按钮类型
        break;
      }
    }
  } catch {
    // 按钮处理错误
  }
}

// ==================== Modal 处理 ====================

/**
 * 处理Modal确认操作（新增/编辑）
 * 根据操作类型执行相应的数据更新逻辑
 * @param values 表单提交的数据
 */
const handleModalConfirm = (values: any) => {
  // 获取modal数据来判断操作类型
  const modalData = modalApi.getData();
  const isEdit = modalData?.isEdit || false;
  const originalRow = modalData?.originalRow;

  withDebounce(
    'modalConfirm',
    async () => {
      if (isEdit && originalRow) {
        // ========== 编辑操作 ==========
        // 查找要更新的行在数组中的位置
        const targetIndex = findRowIndex(originalRow, tableData.value);

        if (targetIndex === -1) {
          message.error('未找到要编辑的数据');
          return;
        }

        // 合并表单数据和原始行的关键字段
        const updatedRowData = {
          ...values, // 表单提交的新数据
          id: originalRow.id, // 保留原始数据库ID
          _customKey: originalRow._customKey, // 保留原始唯一标识
        };

        // 替换数组中的现有数据
        tableData.value[targetIndex] = updatedRowData;
        message.success('编辑成功');
      } else {
        // ========== 新增操作 ==========
        const newRowData = {
          ...values, // 表单提交的数据
          _customKey: generateUniqueKey(), // 生成新的唯一标识
        };

        // 添加到数据数组末尾
        tableData.value.push(newRowData);
        message.success('添加成功');
      }

      // 验证数据完整性
      validateUniqueKeys(tableData.value, 'modalConfirm');

      // 统一更新表格显示
      updateGridData();
    },
    500, // 防抖延迟500ms
  ).catch(() => {
    // 错误处理
    message.error(isEdit ? '编辑失败，请重试' : '添加失败，请重试');
  });
};

// ==================== 计算字段支持 ====================

/**
 * 注册计算字段
 * @param fieldName 字段名
 * @param config 计算配置
 * @param formApi 表单API
 */
const registerCalculationField = (
  fieldName: string,
  config: any,
  formApi?: any,
) => {
  console.log(`[EditTable] 注册计算字段: ${fieldName}`, config);
  calculationManager.registerCalculationField(
    fieldName,
    config,
    formApi,
    tableData,
  );
};

/**
 * 注销计算字段
 * @param fieldName 字段名
 */
const unregisterCalculationField = (fieldName: string) => {
  console.log(`[EditTable] 注销计算字段: ${fieldName}`);
  calculationManager.unregisterCalculationField(fieldName);
};

/**
 * 手动触发计算字段重新计算
 * @param fieldNames 字段名数组（可选，不传则计算所有字段）
 */
const recalculateFields = (fieldNames?: string[]) => {
  const cleanData = tableData.value.map((item) => {
    const { _customKey, _X_ROW_KEY, ...businessData } = item;
    return businessData;
  });

  if (fieldNames && fieldNames.length > 0) {
    fieldNames.forEach((fieldName) => {
      calculationManager.recalculateField(fieldName, cleanData);
    });
  } else {
    calculationManager.recalculateAllFields(cleanData);
  }
};

// 暴露给父组件的方法
defineExpose({
  registerCalculationField,
  unregisterCalculationField,
  recalculateFields,
  generateImportTemplate,
  getTableData: () => tableData.value,
  getCleanTableData: () =>
    tableData.value.map((item) => {
      const { _customKey, _X_ROW_KEY, ...businessData } = item;
      return businessData;
    }),
});
</script>

<template>
  <div class="edit-table-wrapper">
    <!-- 可编辑表格 -->
    <Grid>
      <!-- 工具栏按钮 -->
      <template #toolbar-tools>
        <ToolsButton
          :buttons="props.params?.tablist"
          @click="handleButtonClick"
        />
      </template>
    </Grid>

    <!-- 编辑/新增弹窗 -->
    <Modal @modal-confirm="handleModalConfirm" />

    <!-- Excel导入组件 -->
    <ExcelImport
      ref="excelImportRef"
      :max-size="10"
      :columns="props.params?.columns"
      :validate-data="validateImportData"
      :transform-data="transformImportData"
      :generate-template="generateImportTemplate"
      @success="handleImportSuccess"
      @error="handleImportError"
    />
  </div>
</template>

<style scoped>
.edit-table-wrapper {
  width: 100%;
  min-height: 200px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
}
</style>
