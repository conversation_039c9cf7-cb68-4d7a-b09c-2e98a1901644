# 前端数据转换方法完整后端指南

## 概述

本文档为 PHP 后端开发者提供完整的数据格式规范，涵盖前端三个核心数据转换方法所需的数据结构：

1. **transformColumns** - 表格列配置转换
2. **transformBackendSearchToSchema** - 表单字段配置转换（包括计算字段）
3. **transformToGroupedDescriptions** - 详情页面描述转换

## 目录

- [1. transformColumns - 表格列配置](#1-transformcolumns---表格列配置)
- [2. transformBackendSearchToSchema - 表单字段配置](#2-transformbackendsearchtoshema---表单字段配置)
- [3. 计算字段 (calculated)](#3-计算字段-calculated)
- [4. transformToGroupedDescriptions - 详情描述配置](#4-transformtogroupeddescriptions---详情描述配置)
- [5. 完整的控制器示例](#5-完整的控制器示例)
- [6. 数据验证规则](#6-数据验证规则)
- [7. 最佳实践](#7-最佳实践)

---

## 1. transformColumns - 表格列配置

### 1.1 基础列配置

```php
<?php
// 基础列配置结构
$backendColumns = [
    [
        'field' => 'id',                    // 必填：字段名
        'title' => 'ID',                   // 必填：列标题
        'width' => 80,                     // 可选：列宽度（像素）
        'type' => 'text',                  // 可选：列类型
        'align' => 'center',               // 可选：对齐方式 left|center|right
        'fixed' => 'left',                 // 可选：固定列 left|right|false
        'sortable' => true,                // 可选：是否可排序
        'minWidth' => 60,                  // 可选：最小宽度
        'maxWidth' => 300,                 // 可选：最大宽度
        'resizable' => true,               // 可选：是否可调整大小
        'visible' => true,                 // 可选：是否显示（默认true）
    ],
    [
        'field' => 'name',
        'title' => '姓名',
        'width' => 120,
        'type' => 'text',
        'sortable' => true,
    ],
];
```

### 1.2 支持的列类型

| 类型      | 说明     | 示例           |
| --------- | -------- | -------------- |
| `text`    | 普通文本 | 姓名、描述等   |
| `number`  | 数字     | 价格、数量等   |
| `date`    | 日期     | 创建时间等     |
| `image`   | 图片     | 头像、商品图等 |
| `tag`     | 标签     | 状态、分类等   |
| `actions` | 操作按钮 | 编辑、删除等   |

### 1.3 操作列配置

```php
<?php
// 操作列配置
$actionColumn = [
    'field' => 'actions',
    'title' => '操作',
    'type' => 'actions',
    'width' => 200,
    'fixed' => 'right',
    'actionBtnList' => [
        [
            'key' => 'edit',
            'type' => 'edit',
            'title' => '编辑',
            'color' => 'primary',
        ],
        [
            'key' => 'delete',
            'type' => 'delete',
            'title' => '删除',
            'color' => 'danger',
        ],
    ],
];
```

### 1.4 标签列配置

```php
<?php
// 标签列配置
$tagColumn = [
    'field' => 'status',
    'title' => '状态',
    'type' => 'tag',
    'width' => 100,
    'options' => [
        '1' => ['label' => '启用', 'color' => 'success'],
        '0' => ['label' => '禁用', 'color' => 'error'],
    ],
];
```

---

## 2. transformBackendSearchToSchema - 表单字段配置

### 2.1 基础字段结构

```php
<?php
// 基础字段配置
$backendSearchItem = [
    'field' => 'username',              // 必填：字段名
    'title' => '用户名',                // 必填：显示标题
    'type' => 'input',                  // 必填：字段类型
    'required' => true,                 // 可选：是否必填
    'default' => '',                    // 可选：默认值
    'disabled' => false,                // 可选：是否禁用
    'config' => [                       // 可选：字段配置选项
        'placeholder' => '请输入用户名',
        'allowClear' => true,
        // ... 更多配置
    ],
];
```

### 2.2 支持的字段类型

| 类型         | 说明        | 配置示例                    |
| ------------ | ----------- | --------------------------- |
| `input`      | 文本输入框  | `'placeholder' => '请输入'` |
| `textarea`   | 多行文本    | `'rows' => 4`               |
| `number`     | 数字输入    | `'min' => 0, 'max' => 100`  |
| `select`     | 下拉选择    | `'options' => [...]`        |
| `apiSelect`  | API下拉选择 | `'url' => '/api/options'`   |
| `date`       | 日期选择    | `'format' => 'YYYY-MM-DD'`  |
| `dateRange`  | 日期范围    | `'format' => 'YYYY-MM-DD'`  |
| `radio`      | 单选框      | `'options' => [...]`        |
| `checkbox`   | 复选框      | `'options' => [...]`        |
| `switch`     | 开关        | `'checkedValue' => 1`       |
| `Upload`     | 文件上传    | `'accept' => '.jpg,.png'`   |
| `edittable`  | 可编辑表格  | `'columns' => [...]`        |
| `calculated` | 计算字段    | `'calculation' => [...]`    |
| `hidden`     | 隐藏字段    | 无特殊配置                  |

### 2.3 选项配置格式

```php
<?php
// 数组格式选项
$arrayOptions = [
    ['label' => '选项1', 'value' => 1],
    ['label' => '选项2', 'value' => 2],
];

// 对象格式选项
$objectOptions = [
    '1' => ['label' => '选项1', 'name' => '选项1'],
    '2' => ['label' => '选项2', 'name' => '选项2'],
];
```

### 2.4 API组件配置

```php
<?php
// API选择组件配置
$apiSelectField = [
    'field' => 'department_id',
    'title' => '部门',
    'type' => 'apiSelect',
    'config' => [
        'url' => '/api/departments',
        'labelField' => 'name',
        'valueField' => 'id',
        'immediate' => true,
        'showSearch' => true,
        'params' => ['status' => 1],
    ],
];
```

### 2.5 编辑权限控制

支持根据表单模式（新增/编辑）控制字段的可编辑性：

```php
<?php
// 编辑权限控制示例
$fieldsWithPermission = [
    // 只有新增时可以填写的字段（如用户名）
    [
        'field' => 'username',
        'title' => '用户名',
        'type' => 'input',
        'config' => [
            'editPermission' => 'add-only',
            'placeholder' => '请输入用户名',
        ],
    ],

    // 只有编辑时可以填写的字段（如最后登录时间）
    [
        'field' => 'last_login',
        'title' => '最后登录时间',
        'type' => 'date',
        'config' => [
            'editPermission' => 'edit-only',
        ],
    ],

    // 新增和编辑都可以填写的字段（默认行为）
    [
        'field' => 'email',
        'title' => '邮箱',
        'type' => 'input',
        'config' => [
            'editPermission' => 'both', // 可省略，这是默认值
            'placeholder' => '请输入邮箱',
        ],
    ],

    // 新增和编辑都不可填写的字段（只读显示）
    [
        'field' => 'created_at',
        'title' => '创建时间',
        'type' => 'date',
        'config' => [
            'editPermission' => 'none',
        ],
    ],
];
```

#### 编辑权限类型说明

| 权限类型    | 说明                 | 新增模式  | 编辑模式  |
| ----------- | -------------------- | --------- | --------- |
| `add-only`  | 只有新增时可编辑     | ✅ 可编辑 | ❌ 禁用   |
| `edit-only` | 只有编辑时可编辑     | ❌ 禁用   | ✅ 可编辑 |
| `both`      | 新增和编辑都可编辑   | ✅ 可编辑 | ✅ 可编辑 |
| `none`      | 新增和编辑都不可编辑 | ❌ 禁用   | ❌ 禁用   |

---

## 3. 计算字段 (calculated)

### 3.1 基础配置

```php
<?php
// 计算字段基础配置
$calculatedField = [
    'field' => 'total_amount',
    'title' => '总金额',
    'type' => 'calculated',
    'config' => [
        'placeholder' => '总金额（自动计算）',
        'calculation' => [
            'type' => 'sum',                    // 计算类型
            'sourceFields' => ['amount'],       // 依赖的表格字段
            'precision' => 2,                   // 小数位数
            'defaultValue' => 0,                // 默认值
        ],
        'debounce' => true,                     // 启用防抖
        'debounceDelay' => 300,                 // 防抖延迟（毫秒）
    ],
];
```

### 3.2 计算类型

| 类型      | 说明       | 示例             |
| --------- | ---------- | ---------------- |
| `sum`     | 求和       | 总金额、总数量   |
| `average` | 平均值     | 平均分、平均价格 |
| `product` | 乘积       | 体积、面积       |
| `max`     | 最大值     | 最高价格         |
| `min`     | 最小值     | 最低价格         |
| `count`   | 计数       | 记录数量         |
| `formula` | 自定义公式 | 复杂计算         |

### 3.3 过滤条件

```php
<?php
// 带过滤条件的计算字段
$filteredCalculation = [
    'field' => 'active_count',
    'title' => '活跃数量',
    'type' => 'calculated',
    'config' => [
        'calculation' => [
            'type' => 'count',
            'sourceFields' => ['id'],
            'filter' => [
                [
                    'field' => 'status',
                    'operator' => '=',
                    'value' => 'active',
                ],
            ],
        ],
    ],
];
```

### 3.4 自定义公式

```php
<?php
// 自定义公式计算
$formulaField = [
    'field' => 'profit_margin',
    'title' => '利润率',
    'type' => 'calculated',
    'config' => [
        'calculation' => [
            'type' => 'formula',
            'sourceFields' => ['revenue', 'cost'],
            'formula' => '(field1_sum - field2_sum) / field1_sum * 100',
            'precision' => 2,
        ],
    ],
];
```

---

## 4. transformToGroupedDescriptions - 详情描述配置

### 4.1 基础描述字段

```php
<?php
// 基础描述字段配置
$descriptionRule = [
    'field' => 'username',              // 必填：字段名
    'title' => '用户名',                // 必填：显示标签
    'type' => 'text',                   // 可选：组件类型，默认为 text
    'span' => 1,                        // 可选：跨列数
    'options' => [                      // 可选：选项配置
        'mapping' => [
            '1' => ['label' => '启用', 'color' => 'green'],
            '0' => ['label' => '禁用', 'color' => 'red'],
        ],
    ],
];
```

### 4.2 支持的组件类型

| 类型     | 说明     | 适用场景       |
| -------- | -------- | -------------- |
| `text`   | 普通文本 | 默认文本显示   |
| `tag`    | 标签显示 | 状态、分类等   |
| `badge`  | 徽章显示 | 数量、状态等   |
| `image`  | 图片显示 | 头像、商品图等 |
| `file`   | 文件显示 | 附件、文档等   |
| `date`   | 日期显示 | 时间字段       |
| `rate`   | 评分显示 | 星级评分       |
| `link`   | 链接显示 | 外部链接       |
| `person` | 人员显示 | 用户信息       |
| `dept`   | 部门显示 | 部门信息       |

### 4.3 分组配置

```php
<?php
// 分组描述配置
$groupedDescriptions = [
    [
        'title' => '基础信息',
        'column' => 2,                  // 每行显示列数
        'rules' => [
            [
                'field' => 'name',
                'title' => '姓名',
                'type' => 'text',
            ],
            [
                'field' => 'email',
                'title' => '邮箱',
                'type' => 'text',
            ],
        ],
    ],
    [
        'title' => '状态信息',
        'column' => 3,
        'rules' => [
            [
                'field' => 'status',
                'title' => '状态',
                'type' => 'tag',
                'options' => [
                    'mapping' => [
                        '1' => ['label' => '启用', 'color' => 'success'],
                        '0' => ['label' => '禁用', 'color' => 'error'],
                    ],
                ],
            ],
        ],
    ],
];
```

---

## 5. 完整的控制器示例

### 5.1 用户管理控制器

```php
<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Department;

class UserController extends Controller
{
    /**
     * 获取用户列表表格配置
     */
    public function getTableConfig()
    {
        return response()->json([
            'code' => 200,
            'data' => [
                'columns' => [
                    [
                        'field' => 'id',
                        'title' => 'ID',
                        'width' => 80,
                        'type' => 'text',
                        'align' => 'center',
                        'fixed' => 'left',
                    ],
                    [
                        'field' => 'name',
                        'title' => '姓名',
                        'width' => 120,
                        'type' => 'text',
                        'sortable' => true,
                    ],
                    [
                        'field' => 'email',
                        'title' => '邮箱',
                        'width' => 200,
                        'type' => 'text',
                    ],
                    [
                        'field' => 'department_name',
                        'title' => '部门',
                        'width' => 150,
                        'type' => 'text',
                    ],
                    [
                        'field' => 'status',
                        'title' => '状态',
                        'width' => 100,
                        'type' => 'tag',
                        'options' => [
                            '1' => ['label' => '启用', 'color' => 'success'],
                            '0' => ['label' => '禁用', 'color' => 'error'],
                        ],
                    ],
                    [
                        'field' => 'created_at',
                        'title' => '创建时间',
                        'width' => 180,
                        'type' => 'date',
                    ],
                    [
                        'field' => 'actions',
                        'title' => '操作',
                        'type' => 'actions',
                        'width' => 200,
                        'fixed' => 'right',
                        'actionBtnList' => [
                            [
                                'key' => 'edit',
                                'type' => 'edit',
                                'title' => '编辑',
                                'color' => 'primary',
                            ],
                            [
                                'key' => 'delete',
                                'type' => 'delete',
                                'title' => '删除',
                                'color' => 'danger',
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    }

    /**
     * 获取用户表单配置
     */
    public function getFormConfig()
    {
        return response()->json([
            'code' => 200,
            'data' => [
                [
                    'field' => 'name',
                    'title' => '姓名',
                    'type' => 'input',
                    'required' => true,
                    'config' => [
                        'placeholder' => '请输入姓名',
                    ],
                ],
                [
                    'field' => 'email',
                    'title' => '邮箱',
                    'type' => 'input',
                    'required' => true,
                    'config' => [
                        'placeholder' => '请输入邮箱',
                    ],
                ],
                [
                    'field' => 'department_id',
                    'title' => '部门',
                    'type' => 'apiSelect',
                    'required' => true,
                    'config' => [
                        'url' => '/api/departments',
                        'labelField' => 'name',
                        'valueField' => 'id',
                        'placeholder' => '请选择部门',
                    ],
                ],
                [
                    'field' => 'status',
                    'title' => '状态',
                    'type' => 'radio',
                    'default' => 1,
                    'config' => [
                        'options' => [
                            ['label' => '启用', 'value' => 1],
                            ['label' => '禁用', 'value' => 0],
                        ],
                    ],
                ],
            ],
        ]);
    }

    /**
     * 获取用户详情配置
     */
    public function getDetailConfig()
    {
        return response()->json([
            'code' => 200,
            'data' => [
                [
                    'title' => '基础信息',
                    'column' => 2,
                    'rules' => [
                        [
                            'field' => 'name',
                            'title' => '姓名',
                            'type' => 'text',
                        ],
                        [
                            'field' => 'email',
                            'title' => '邮箱',
                            'type' => 'text',
                        ],
                        [
                            'field' => 'department_name',
                            'title' => '部门',
                            'type' => 'text',
                        ],
                        [
                            'field' => 'status',
                            'title' => '状态',
                            'type' => 'tag',
                            'options' => [
                                'mapping' => [
                                    '1' => ['label' => '启用', 'color' => 'success'],
                                    '0' => ['label' => '禁用', 'color' => 'error'],
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'title' => '时间信息',
                    'column' => 2,
                    'rules' => [
                        [
                            'field' => 'created_at',
                            'title' => '创建时间',
                            'type' => 'date',
                        ],
                        [
                            'field' => 'updated_at',
                            'title' => '更新时间',
                            'type' => 'date',
                        ],
                    ],
                ],
            ],
        ]);
    }
}
```

---

## 6. 数据验证规则

### 6.1 字段验证函数

```php
<?php
/**
 * 验证表格列配置
 */
function validateTableColumns($columns) {
    foreach ($columns as $column) {
        // 必填字段验证
        $required = ['field', 'title'];
        foreach ($required as $key) {
            if (!isset($column[$key]) || empty($column[$key])) {
                throw new InvalidArgumentException("Column field '{$key}' is required");
            }
        }

        // 类型验证
        $validTypes = ['text', 'number', 'date', 'image', 'tag', 'actions'];
        if (isset($column['type']) && !in_array($column['type'], $validTypes)) {
            throw new InvalidArgumentException("Invalid column type: {$column['type']}");
        }
    }
    return true;
}

/**
 * 验证表单字段配置
 */
function validateFormFields($fields) {
    foreach ($fields as $field) {
        // 必填字段验证
        $required = ['field', 'title', 'type'];
        foreach ($required as $key) {
            if (!isset($field[$key]) || empty($field[$key])) {
                throw new InvalidArgumentException("Form field '{$key}' is required");
            }
        }

        // 类型验证
        $validTypes = [
            'input', 'textarea', 'number', 'select', 'apiSelect',
            'date', 'dateRange', 'radio', 'checkbox', 'switch',
            'Upload', 'edittable', 'calculated', 'hidden'
        ];
        if (!in_array($field['type'], $validTypes)) {
            throw new InvalidArgumentException("Invalid field type: {$field['type']}");
        }

        // 计算字段特殊验证
        if ($field['type'] === 'calculated') {
            if (!isset($field['config']['calculation'])) {
                throw new InvalidArgumentException("Calculation config is required for calculated field");
            }

            $calculation = $field['config']['calculation'];
            if (!isset($calculation['type']) || !isset($calculation['sourceFields'])) {
                throw new InvalidArgumentException("Calculation type and sourceFields are required");
            }
        }
    }
    return true;
}

/**
 * 验证详情描述配置
 */
function validateDescriptionRules($groups) {
    foreach ($groups as $group) {
        if (!isset($group['rules']) || !is_array($group['rules'])) {
            throw new InvalidArgumentException("Group rules are required and must be an array");
        }

        foreach ($group['rules'] as $rule) {
            $required = ['field', 'title'];
            foreach ($required as $key) {
                if (!isset($rule[$key]) || empty($rule[$key])) {
                    throw new InvalidArgumentException("Description rule '{$key}' is required");
                }
            }
        }
    }
    return true;
}
```

---

## 7. 最佳实践

### 7.1 命名规范

```php
<?php
// 推荐的字段命名
$goodNaming = [
    // 表格列
    'user_name',        // 用户名
    'created_at',       // 创建时间
    'status',           // 状态
    'department_name',  // 部门名称

    // 表单字段
    'email',            // 邮箱
    'phone_number',     // 电话号码
    'birth_date',       // 出生日期

    // 计算字段
    'total_amount',     // 总金额
    'average_score',    // 平均分
    'item_count',       // 项目数量
];

// 避免的字段命名
$badNaming = [
    'field1',           // 不明确的命名
    'temp_data',        // 临时性命名
    'calc_result',      // 过于通用
];
```

### 7.2 性能优化建议

```php
<?php
// 1. 合理设置列宽度
$optimizedColumns = [
    [
        'field' => 'id',
        'width' => 80,          // ID列设置较小宽度
        'minWidth' => 60,
        'maxWidth' => 100,
    ],
    [
        'field' => 'description',
        'minWidth' => 200,      // 描述列设置最小宽度
        'resizable' => true,    // 允许调整大小
    ],
];

// 2. 合理使用固定列
$fixedColumns = [
    [
        'field' => 'id',
        'fixed' => 'left',      // 重要字段固定在左侧
    ],
    [
        'field' => 'actions',
        'fixed' => 'right',     // 操作列固定在右侧
    ],
];

// 3. 计算字段防抖设置
$optimizedCalculation = [
    'config' => [
        'debounce' => true,
        'debounceDelay' => 300,  // 300ms通常是合适的
    ],
];
```

### 7.3 错误处理

```php
<?php
// 设置合理的默认值和错误处理
$safeConfig = [
    // 表格列默认配置
    'columns' => [
        [
            'field' => 'name',
            'title' => '姓名',
            'type' => 'text',
            'width' => 120,
            'visible' => true,      // 明确设置可见性
            'sortable' => false,    // 明确设置排序
        ],
    ],

    // 表单字段默认配置
    'fields' => [
        [
            'field' => 'email',
            'title' => '邮箱',
            'type' => 'input',
            'required' => false,    // 明确设置必填性
            'config' => [
                'placeholder' => '请输入邮箱',
                'allowClear' => true,
            ],
        ],
    ],

    // 计算字段安全配置
    'calculatedField' => [
        'config' => [
            'calculation' => [
                'type' => 'sum',
                'sourceFields' => ['amount'],
                'defaultValue' => 0,        // 设置默认值
                'precision' => 2,           // 控制精度
            ],
        ],
    ],
];
```

---

## 8. 常见问题解答

### Q: 如何处理大量数据的表格列配置？

A: 建议使用分页、虚拟滚动，并合理设置列的显示/隐藏，避免一次性渲染过多列。

### Q: 计算字段支持嵌套依赖吗？

A: 目前不支持计算字段之间的依赖关系，只支持依赖表格数据字段。

### Q: API组件如何处理请求失败？

A: 建议在后端提供默认的空数据响应，前端会自动处理并显示空状态。

### Q: 如何优化表单字段的加载性能？

A: 使用 `immediate: false` 延迟加载非必要的API组件，使用防抖减少请求频率。

### Q: 详情页面如何处理空值显示？

A: 系统会自动将空值显示为 "-"，也可以通过 `transform` 函数自定义空值显示。

---

## 9. 版本更新说明

### v2.0.0 新增功能

- 新增计算字段 (calculated) 类型支持
- 增强API组件的配置选项
- 优化表格操作列的按钮配置
- 改进详情页面的分组显示

### v1.5.0 功能改进

- 优化字段联动机制
- 增加更多组件类型支持
- 改进错误处理和验证

---

## 10. 技术支持

如有问题或建议，请联系开发团队或查看相关技术文档。

**文档版本**: v2.0.0 **最后更新**: 2024-07-30
