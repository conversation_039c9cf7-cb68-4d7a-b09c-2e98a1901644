/**
 * 计算引擎精度测试
 * 测试JavaScript浮点数精度问题的解决方案
 */

import { calculationEngine } from '../calculation-engine';
import type { CalculationConfig } from '../calculation-engine';

describe('计算引擎精度测试', () => {
  // 测试数据：包含会导致精度问题的浮点数
  const testData = [
    { price: 0.1, quantity: 3, discount: 0.05 },
    { price: 0.2, quantity: 2, discount: 0.1 },
    { price: 0.3, quantity: 1, discount: 0.15 },
  ];

  describe('求和计算精度测试', () => {
    test('小数求和应该精确', () => {
      const config: CalculationConfig = {
        targetField: 'total_price',
        rule: {
          type: 'sum',
          sourceFields: ['price'],
          precision: 2,
          defaultValue: 0,
        },
      };

      const result = calculationEngine.calculate(testData, config);
      
      // 0.1 + 0.2 + 0.3 = 0.6 (而不是 0.6000000000000001)
      expect(result).toBe(0.6);
      expect(typeof result).toBe('number');
    });

    test('多字段求和应该精确', () => {
      const config: CalculationConfig = {
        targetField: 'total_with_discount',
        rule: {
          type: 'sum',
          sourceFields: ['price', 'discount'],
          precision: 2,
          defaultValue: 0,
        },
      };

      const result = calculationEngine.calculate(testData, config);
      
      // (0.1+0.05) + (0.2+0.1) + (0.3+0.15) = 0.9
      expect(result).toBe(0.9);
    });
  });

  describe('乘积计算精度测试', () => {
    test('小数乘积应该精确', () => {
      const config: CalculationConfig = {
        targetField: 'total_volume',
        rule: {
          type: 'product',
          sourceFields: ['price'],
          precision: 3,
          defaultValue: 0,
        },
      };

      const result = calculationEngine.calculate(testData, config);
      
      // 0.1 * 0.2 * 0.3 = 0.006
      expect(result).toBe(0.006);
    });

    test('多字段乘积应该精确', () => {
      const testDataForProduct = [
        { length: 1.1, width: 2.2, height: 3.3 },
        { length: 0.5, width: 0.6, height: 0.7 },
      ];

      const config: CalculationConfig = {
        targetField: 'total_volume',
        rule: {
          type: 'product',
          sourceFields: ['length', 'width', 'height'],
          precision: 4,
          defaultValue: 0,
        },
      };

      const result = calculationEngine.calculate(testDataForProduct, config);
      
      // (1.1*2.2*3.3) * (0.5*0.6*0.7) = 7.986 * 0.21 = 1.6771
      expect(result).toBe(1.6771);
    });
  });

  describe('平均值计算精度测试', () => {
    test('小数平均值应该精确', () => {
      const config: CalculationConfig = {
        targetField: 'average_price',
        rule: {
          type: 'average',
          sourceFields: ['price'],
          precision: 2,
          defaultValue: 0,
        },
      };

      const result = calculationEngine.calculate(testData, config);
      
      // (0.1 + 0.2 + 0.3) / 3 = 0.6 / 3 = 0.2
      expect(result).toBe(0.2);
    });
  });

  describe('自定义公式精度测试', () => {
    test('复杂公式计算应该精确', () => {
      const config: CalculationConfig = {
        targetField: 'profit_margin',
        rule: {
          type: 'formula',
          sourceFields: ['price', 'discount'],
          formula: '(field1_sum - field2_sum) / field1_sum * 100',
          precision: 2,
          defaultValue: 0,
        },
      };

      const result = calculationEngine.calculate(testData, config);
      
      // (0.6 - 0.3) / 0.6 * 100 = 0.3 / 0.6 * 100 = 50
      expect(result).toBe(50);
    });
  });

  describe('边界情况测试', () => {
    test('处理NaN和Infinity', () => {
      const invalidData = [
        { value: NaN },
        { value: Infinity },
        { value: -Infinity },
        { value: 0 },
      ];

      const config: CalculationConfig = {
        targetField: 'safe_sum',
        rule: {
          type: 'sum',
          sourceFields: ['value'],
          precision: 2,
          defaultValue: 0,
        },
      };

      const result = calculationEngine.calculate(invalidData, config);
      
      // 应该只计算有效的数字（0），其他无效值被转换为0
      expect(result).toBe(0);
      expect(isFinite(result)).toBe(true);
    });

    test('处理空数组', () => {
      const config: CalculationConfig = {
        targetField: 'empty_sum',
        rule: {
          type: 'sum',
          sourceFields: ['value'],
          precision: 2,
          defaultValue: 100,
        },
      };

      const result = calculationEngine.calculate([], config);
      
      expect(result).toBe(100); // 应该返回默认值
    });

    test('处理非数字字符串', () => {
      const mixedData = [
        { value: '1.5' },
        { value: 'abc' },
        { value: '2.5' },
        { value: null },
        { value: undefined },
      ];

      const config: CalculationConfig = {
        targetField: 'mixed_sum',
        rule: {
          type: 'sum',
          sourceFields: ['value'],
          precision: 1,
          defaultValue: 0,
        },
      };

      const result = calculationEngine.calculate(mixedData, config);
      
      // 应该只计算能转换为数字的值：1.5 + 2.5 = 4.0
      expect(result).toBe(4.0);
    });
  });

  describe('精度配置测试', () => {
    test('不同精度配置应该正确工作', () => {
      const precisionTestData = [
        { value: 1.23456789 },
        { value: 2.34567890 },
      ];

      // 测试0位小数
      const config0 = {
        targetField: 'sum_0',
        rule: { type: 'sum' as const, sourceFields: ['value'], precision: 0 },
      };
      expect(calculationEngine.calculate(precisionTestData, config0)).toBe(4);

      // 测试2位小数
      const config2 = {
        targetField: 'sum_2',
        rule: { type: 'sum' as const, sourceFields: ['value'], precision: 2 },
      };
      expect(calculationEngine.calculate(precisionTestData, config2)).toBe(3.58);

      // 测试4位小数
      const config4 = {
        targetField: 'sum_4',
        rule: { type: 'sum' as const, sourceFields: ['value'], precision: 4 },
      };
      expect(calculationEngine.calculate(precisionTestData, config4)).toBe(3.5802);
    });
  });
});

// 性能测试（可选）
describe('计算引擎性能测试', () => {
  test('大数据量计算性能', () => {
    // 生成大量测试数据
    const largeData = Array.from({ length: 10000 }, (_, i) => ({
      value: Math.random() * 100,
      index: i,
    }));

    const config: CalculationConfig = {
      targetField: 'large_sum',
      rule: {
        type: 'sum',
        sourceFields: ['value'],
        precision: 2,
        defaultValue: 0,
      },
    };

    const startTime = performance.now();
    const result = calculationEngine.calculate(largeData, config);
    const endTime = performance.now();

    expect(typeof result).toBe('number');
    expect(isFinite(result)).toBe(true);
    expect(endTime - startTime).toBeLessThan(100); // 应该在100ms内完成
  });
});

// 导出测试工具函数供其他测试使用
export const testUtils = {
  createTestData: (count: number) => {
    return Array.from({ length: count }, (_, i) => ({
      id: i + 1,
      value: Math.random() * 100,
      price: Math.round(Math.random() * 1000) / 100, // 保持2位小数
      quantity: Math.floor(Math.random() * 10) + 1,
    }));
  },
  
  expectPrecision: (actual: number, expected: number, precision: number = 2) => {
    const factor = Math.pow(10, precision);
    expect(Math.round(actual * factor) / factor).toBe(expected);
  },
};
