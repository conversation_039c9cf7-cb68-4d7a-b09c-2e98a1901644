/**
 * 求和精度处理策略对比
 * 展示"先加完再四舍五入" vs "四舍五入后再相加"的差异
 */

console.log('=== 求和精度处理策略对比 ===\n');

// 测试数据：包含需要四舍五入的小数
const testData = [
  { amount: 12.345 },  // 四舍五入到2位: 12.35
  { amount: 23.344 },  // 四舍五入到2位: 23.34
  { amount: 34.346 },  // 四舍五入到2位: 34.35
];

console.log('测试数据:');
testData.forEach((item, index) => {
  console.log(`  ${index + 1}. ${item.amount}`);
});
console.log('');

// 精度处理函数
function roundToPrecision(num, precision) {
  const factor = Math.pow(10, precision);
  return Math.round((num + Number.EPSILON) * factor) / factor;
}

// 策略1: 先加完再四舍五入（当前实现）
function sumThenRound(data, precision) {
  const sum = data.reduce((total, item) => total + item.amount, 0);
  return roundToPrecision(sum, precision);
}

// 策略2: 四舍五入后再相加
function roundThenSum(data, precision) {
  const roundedValues = data.map(item => roundToPrecision(item.amount, precision));
  return roundedValues.reduce((total, val) => total + val, 0);
}

// 对比结果
const precision = 2;

console.log('=== 策略对比（保留2位小数）===');
console.log('');

console.log('策略1: 先加完再四舍五入');
const strategy1Result = sumThenRound(testData, precision);
console.log(`  计算过程: (12.345 + 23.344 + 34.346) = ${testData.reduce((sum, item) => sum + item.amount, 0)}`);
console.log(`  四舍五入: ${strategy1Result}`);
console.log('');

console.log('策略2: 四舍五入后再相加');
const strategy2Result = roundThenSum(testData, precision);
const roundedValues = testData.map(item => roundToPrecision(item.amount, precision));
console.log(`  四舍五入: ${testData.map(item => item.amount).join(' + ')} → ${roundedValues.join(' + ')}`);
console.log(`  相加结果: ${strategy2Result}`);
console.log('');

console.log('结果对比:');
console.log(`  策略1结果: ${strategy1Result}`);
console.log(`  策略2结果: ${strategy2Result}`);
console.log(`  差异: ${Math.abs(strategy1Result - strategy2Result)}`);
console.log('');

// 更多测试用例
console.log('=== 更多测试用例 ===');

const testCases = [
  {
    name: '财务计算场景',
    data: [
      { amount: 100.125 },
      { amount: 200.124 },
      { amount: 300.126 },
    ],
  },
  {
    name: '小额计算场景',
    data: [
      { amount: 0.125 },
      { amount: 0.124 },
      { amount: 0.126 },
    ],
  },
  {
    name: '混合计算场景',
    data: [
      { amount: 1234.567 },
      { amount: 0.123 },
      { amount: 56.789 },
    ],
  },
];

testCases.forEach((testCase, index) => {
  console.log(`${index + 1}. ${testCase.name}:`);
  
  const result1 = sumThenRound(testCase.data, 2);
  const result2 = roundThenSum(testCase.data, 2);
  const difference = Math.abs(result1 - result2);
  
  console.log(`   先加后舍: ${result1}`);
  console.log(`   先舍后加: ${result2}`);
  console.log(`   差异: ${difference}`);
  console.log('');
});

// 业务场景分析
console.log('=== 业务场景分析 ===');
console.log('');

console.log('1. 财务会计场景:');
console.log('   - 通常采用"先加完再四舍五入"');
console.log('   - 原因: 保持计算的连续性，避免中间舍入误差累积');
console.log('   - 符合会计准则和税务要求');
console.log('');

console.log('2. 显示精度场景:');
console.log('   - 可能采用"四舍五入后再相加"');
console.log('   - 原因: 用户看到的每个数字都是已经格式化的');
console.log('   - 保持界面显示的一致性');
console.log('');

console.log('3. 统计分析场景:');
console.log('   - 通常采用"先加完再四舍五入"');
console.log('   - 原因: 保持数据的原始精度，减少统计误差');
console.log('   - 提高分析结果的准确性');
console.log('');

// 推荐策略
console.log('=== 推荐策略 ===');
console.log('');
console.log('当前实现: 先加完再四舍五入 ✅');
console.log('');
console.log('优点:');
console.log('  ✅ 数学上更准确');
console.log('  ✅ 符合财务会计标准');
console.log('  ✅ 减少累积误差');
console.log('  ✅ 适合大多数业务场景');
console.log('');
console.log('如果需要支持"先舍后加"策略，可以通过配置参数控制');

// 配置化方案示例
console.log('');
console.log('=== 配置化方案示例 ===');

const configExample = {
  field: 'total_amount',
  title: '总金额',
  type: 'calculated',
  config: {
    calculation: {
      type: 'sum',
      sourceFields: ['amount'],
      precision: 2,
      roundingStrategy: 'sum_then_round', // 或 'round_then_sum'
      defaultValue: 0,
    },
  },
};

console.log('配置示例:');
console.log(JSON.stringify(configExample, null, 2));
console.log('');

console.log('roundingStrategy 选项:');
console.log('  - "sum_then_round": 先加完再四舍五入（推荐，当前默认）');
console.log('  - "round_then_sum": 四舍五入后再相加');
console.log('');

console.log('=== 总结 ===');
console.log('');
console.log('当前实现采用"先加完再四舍五入"策略，这是最佳实践：');
console.log('1. 数学上更准确，减少舍入误差');
console.log('2. 符合财务和会计标准');
console.log('3. 适合绝大多数业务场景');
console.log('4. 如有特殊需求，可通过配置参数扩展支持其他策略');
