# 后端计算字段完整指南

## 概述

计算字段是一种特殊的表单字段类型，它的值不是由用户直接输入，而是根据表格数据进行自动计算得出。本文档详细说明了如何在后端配置计算字段，以及前端如何处理这些配置。

## 数据结构规范

### 基础字段结构

```php
<?php
// 计算字段的基础结构
$calculatedField = [
    'field' => 'total_amount',           // 必填：字段名
    'title' => '总金额',                 // 必填：显示标题
    'type' => 'calculated',              // 必填：字段类型
    'required' => false,                 // 可选：是否必填（通常为false）
    'default' => 0,                      // 可选：默认值
    'config' => [                        // 必填：字段配置
        'placeholder' => '总金额（自动计算）',
        'calculation' => [               // 必填：计算配置
            'type' => 'sum',             // 必填：计算类型
            'sourceFields' => ['amount'], // 必填：依赖的表格字段
            'precision' => 2,            // 可选：小数位数，默认2
            'defaultValue' => 0,         // 可选：计算失败时的默认值
            'filter' => [],              // 可选：过滤条件
            'formula' => '',             // 可选：自定义公式（type为formula时必填）
        ],
        'debounce' => true,              // 可选：是否启用防抖，默认true
        'debounceDelay' => 300,          // 可选：防抖延迟（毫秒），默认300
    ],
];
```

## 计算类型详解

### 1. 求和计算 (sum)

适用于对数值字段进行求和操作。

```php
<?php
// 单字段求和
$sumField = [
    'field' => 'total_price',
    'title' => '总价格',
    'type' => 'calculated',
    'config' => [
        'calculation' => [
            'type' => 'sum',
            'sourceFields' => ['price'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];

// 多字段求和（每行对应字段相加后再求总和）
$multiSumField = [
    'field' => 'total_cost',
    'title' => '总成本',
    'type' => 'calculated',
    'config' => [
        'calculation' => [
            'type' => 'sum',
            'sourceFields' => ['material_cost', 'labor_cost', 'other_cost'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];
```

### 2. 平均值计算 (average)

计算指定字段的平均值。

```php
<?php
$averageField = [
    'field' => 'average_score',
    'title' => '平均分',
    'type' => 'calculated',
    'config' => [
        'calculation' => [
            'type' => 'average',
            'sourceFields' => ['score'],
            'precision' => 1,
            'defaultValue' => 0,
        ],
    ],
];
```

### 3. 乘积计算 (product)

计算指定字段的乘积，常用于体积、面积等计算。

```php
<?php
$productField = [
    'field' => 'total_volume',
    'title' => '总体积',
    'type' => 'calculated',
    'config' => [
        'calculation' => [
            'type' => 'product',
            'sourceFields' => ['length', 'width', 'height'],
            'precision' => 3,
            'defaultValue' => 0,
        ],
    ],
];
```

### 4. 最大值/最小值计算 (max/min)

找出指定字段中的最大值或最小值。

```php
<?php
$maxField = [
    'field' => 'max_price',
    'title' => '最高价格',
    'type' => 'calculated',
    'config' => [
        'calculation' => [
            'type' => 'max',
            'sourceFields' => ['price'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];

$minField = [
    'field' => 'min_price',
    'title' => '最低价格',
    'type' => 'calculated',
    'config' => [
        'calculation' => [
            'type' => 'min',
            'sourceFields' => ['price'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];
```

### 5. 计数 (count)

统计记录数量，可以用任意字段进行计数。

```php
<?php
$countField = [
    'field' => 'item_count',
    'title' => '项目数量',
    'type' => 'calculated',
    'config' => [
        'calculation' => [
            'type' => 'count',
            'sourceFields' => ['id'],  // 任意字段都可以用于计数
            'defaultValue' => 0,
        ],
    ],
];
```

### 6. 自定义公式计算 (formula)

使用自定义数学公式进行复杂计算。

```php
<?php
$formulaField = [
    'field' => 'profit_margin',
    'title' => '利润率',
    'type' => 'calculated',
    'config' => [
        'calculation' => [
            'type' => 'formula',
            'sourceFields' => ['revenue', 'cost'],
            'formula' => '(field1_sum - field2_sum) / field1_sum * 100',
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];
```

## 过滤条件配置

计算字段支持对表格数据进行过滤，只对满足条件的数据进行计算。

### 基础过滤配置

```php
<?php
$filteredCalculation = [
    'field' => 'active_user_count',
    'title' => '活跃用户数',
    'type' => 'calculated',
    'config' => [
        'calculation' => [
            'type' => 'count',
            'sourceFields' => ['user_id'],
            'filter' => [
                [
                    'field' => 'status',
                    'operator' => '=',
                    'value' => 'active',
                ],
                [
                    'field' => 'last_login',
                    'operator' => '>',
                    'value' => '2024-01-01',
                ],
            ],
            'defaultValue' => 0,
        ],
    ],
];
```

### 支持的过滤操作符

| 操作符   | 说明     | 示例                         | 适用数据类型 |
| -------- | -------- | ---------------------------- | ------------ |
| `=`      | 等于     | `'value' => 'active'`        | 所有类型     |
| `!=`     | 不等于   | `'value' => 'inactive'`      | 所有类型     |
| `>`      | 大于     | `'value' => 100`             | 数值、日期   |
| `<`      | 小于     | `'value' => 1000`            | 数值、日期   |
| `>=`     | 大于等于 | `'value' => 18`              | 数值、日期   |
| `<=`     | 小于等于 | `'value' => 65`              | 数值、日期   |
| `in`     | 包含于   | `'value' => ['A', 'B', 'C']` | 所有类型     |
| `not_in` | 不包含于 | `'value' => ['X', 'Y', 'Z']` | 所有类型     |

### 多条件过滤示例

```php
<?php
// 复杂过滤条件：统计高分且活跃的用户数
$complexFilter = [
    'field' => 'high_score_active_users',
    'title' => '高分活跃用户数',
    'type' => 'calculated',
    'config' => [
        'calculation' => [
            'type' => 'count',
            'sourceFields' => ['user_id'],
            'filter' => [
                [
                    'field' => 'status',
                    'operator' => '=',
                    'value' => 'active',
                ],
                [
                    'field' => 'score',
                    'operator' => '>=',
                    'value' => 80,
                ],
                [
                    'field' => 'user_type',
                    'operator' => 'in',
                    'value' => ['premium', 'vip'],
                ],
            ],
            'defaultValue' => 0,
        ],
    ],
];
```

## 自定义公式语法

### 可用变量

在自定义公式中，可以使用以下预定义变量：

- `field1_sum`, `field2_sum`, ... - 各字段的求和值
- `field1_avg`, `field2_avg`, ... - 各字段的平均值
- `field1_count`, `field2_count`, ... - 各字段的计数
- `field1_max`, `field2_max`, ... - 各字段的最大值
- `field1_min`, `field2_min`, ... - 各字段的最小值

### 支持的运算符

- 基础运算：`+`, `-`, `*`, `/`
- 括号：`(`, `)`
- 数字常量：`123`, `3.14`

### 公式示例

```php
<?php
// 利润率计算
$profitMargin = [
    'type' => 'formula',
    'sourceFields' => ['revenue', 'cost'],
    'formula' => '(field1_sum - field2_sum) / field1_sum * 100',
];

// 加权平均分
$weightedAverage = [
    'type' => 'formula',
    'sourceFields' => ['score', 'weight'],
    'formula' => 'field1_sum / field2_sum',
];

// 复合增长率
$growthRate = [
    'type' => 'formula',
    'sourceFields' => ['current_value', 'initial_value'],
    'formula' => '(field1_avg / field2_avg - 1) * 100',
];
```

## 完整的控制器示例

### 订单管理控制器

```php
<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\OrderItem;

class OrderController extends Controller
{
    /**
     * 获取订单表单配置
     */
    public function getFormConfig()
    {
        return response()->json([
            'code' => 200,
            'data' => [
                // 基础信息
                [
                    'field' => 'order_no',
                    'title' => '订单号',
                    'type' => 'input',
                    'required' => true,
                    'config' => [
                        'placeholder' => '请输入订单号',
                    ],
                ],
                [
                    'field' => 'customer_name',
                    'title' => '客户名称',
                    'type' => 'input',
                    'required' => true,
                    'config' => [
                        'placeholder' => '请输入客户名称',
                    ],
                ],

                // 订单明细表格
                [
                    'field' => 'order_items',
                    'title' => '订单明细',
                    'type' => 'edittable',
                    'config' => [
                        'columns' => [
                            [
                                'field' => 'product_name',
                                'title' => '商品名称',
                                'type' => 'input',
                                'width' => 200,
                            ],
                            [
                                'field' => 'quantity',
                                'title' => '数量',
                                'type' => 'number',
                                'width' => 100,
                            ],
                            [
                                'field' => 'unit_price',
                                'title' => '单价',
                                'type' => 'number',
                                'width' => 120,
                            ],
                            [
                                'field' => 'discount_rate',
                                'title' => '折扣率',
                                'type' => 'number',
                                'width' => 100,
                            ],
                            [
                                'field' => 'amount',
                                'title' => '小计',
                                'type' => 'number',
                                'width' => 120,
                            ],
                        ],
                    ],
                ],

                // 计算字段：订单总金额
                [
                    'field' => 'total_amount',
                    'title' => '订单总金额',
                    'type' => 'calculated',
                    'config' => [
                        'placeholder' => '订单总金额（自动计算）',
                        'calculation' => [
                            'type' => 'sum',
                            'sourceFields' => ['amount'],
                            'precision' => 2,
                            'defaultValue' => 0,
                        ],
                    ],
                ],

                // 计算字段：商品总数量
                [
                    'field' => 'total_quantity',
                    'title' => '商品总数量',
                    'type' => 'calculated',
                    'config' => [
                        'placeholder' => '商品总数量（自动计算）',
                        'calculation' => [
                            'type' => 'sum',
                            'sourceFields' => ['quantity'],
                            'precision' => 0,
                            'defaultValue' => 0,
                        ],
                    ],
                ],

                // 计算字段：平均单价
                [
                    'field' => 'average_price',
                    'title' => '平均单价',
                    'type' => 'calculated',
                    'config' => [
                        'placeholder' => '平均单价（自动计算）',
                        'calculation' => [
                            'type' => 'average',
                            'sourceFields' => ['unit_price'],
                            'precision' => 2,
                            'defaultValue' => 0,
                        ],
                    ],
                ],

                // 计算字段：总折扣金额（自定义公式）
                [
                    'field' => 'total_discount',
                    'title' => '总折扣金额',
                    'type' => 'calculated',
                    'config' => [
                        'placeholder' => '总折扣金额（自动计算）',
                        'calculation' => [
                            'type' => 'formula',
                            'sourceFields' => ['unit_price', 'quantity', 'discount_rate'],
                            'formula' => 'field1_sum * field2_sum * field3_avg',
                            'precision' => 2,
                            'defaultValue' => 0,
                        ],
                    ],
                ],

                // 计算字段：有折扣商品数量（带过滤条件）
                [
                    'field' => 'discounted_items_count',
                    'title' => '有折扣商品数量',
                    'type' => 'calculated',
                    'config' => [
                        'placeholder' => '有折扣商品数量（自动计算）',
                        'calculation' => [
                            'type' => 'count',
                            'sourceFields' => ['product_name'],
                            'filter' => [
                                [
                                    'field' => 'discount_rate',
                                    'operator' => '>',
                                    'value' => 0,
                                ],
                            ],
                            'defaultValue' => 0,
                        ],
                    ],
                ],

                // 其他字段
                [
                    'field' => 'remark',
                    'title' => '备注',
                    'type' => 'textarea',
                    'config' => [
                        'placeholder' => '请输入备注信息',
                        'rows' => 3,
                    ],
                ],
            ],
        ]);
    }

    /**
     * 保存订单
     */
    public function store(Request $request)
    {
        $data = $request->all();

        // 创建订单
        $order = Order::create([
            'order_no' => $data['order_no'],
            'customer_name' => $data['customer_name'],
            'total_amount' => $data['total_amount'],
            'total_quantity' => $data['total_quantity'],
            'remark' => $data['remark'] ?? '',
        ]);

        // 创建订单明细
        if (isset($data['order_items']) && is_array($data['order_items'])) {
            foreach ($data['order_items'] as $item) {
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_name' => $item['product_name'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'discount_rate' => $item['discount_rate'] ?? 0,
                    'amount' => $item['amount'],
                ]);
            }
        }

        return response()->json([
            'code' => 200,
            'message' => '订单创建成功',
            'data' => $order,
        ]);
    }
}
```

## 数据验证规则

### 字段验证

```php
<?php
function validateCalculatedField($field) {
    // 必填字段验证
    $required = ['field', 'title', 'type'];
    foreach ($required as $key) {
        if (!isset($field[$key]) || empty($field[$key])) {
            throw new InvalidArgumentException("Field '{$key}' is required");
        }
    }

    // 类型验证
    if ($field['type'] !== 'calculated') {
        throw new InvalidArgumentException("Invalid field type: {$field['type']}");
    }

    // 计算配置验证
    if (!isset($field['config']['calculation'])) {
        throw new InvalidArgumentException("Calculation config is required for calculated field");
    }

    $calculation = $field['config']['calculation'];

    // 计算类型验证
    $validTypes = ['sum', 'average', 'product', 'max', 'min', 'count', 'formula'];
    if (!in_array($calculation['type'], $validTypes)) {
        throw new InvalidArgumentException("Invalid calculation type: {$calculation['type']}");
    }

    // 源字段验证
    if (!isset($calculation['sourceFields']) || !is_array($calculation['sourceFields']) || empty($calculation['sourceFields'])) {
        throw new InvalidArgumentException("sourceFields is required and must be a non-empty array");
    }

    // 公式类型特殊验证
    if ($calculation['type'] === 'formula' && empty($calculation['formula'])) {
        throw new InvalidArgumentException("Formula is required when calculation type is 'formula'");
    }

    return true;
}
```

## 最佳实践

### 1. 字段命名规范

```php
<?php
// 推荐的字段命名
$goodNaming = [
    'total_amount',      // 总金额
    'average_score',     // 平均分
    'item_count',        // 项目数量
    'max_price',         // 最高价格
    'profit_margin',     // 利润率
];

// 避免的字段命名
$badNaming = [
    'calc1',            // 不明确的命名
    'field_sum',        // 过于通用
    'temp_value',       // 临时性命名
];
```

### 2. 性能优化建议

```php
<?php
// 合理设置防抖延迟
$optimizedField = [
    'config' => [
        'debounce' => true,
        'debounceDelay' => 300,  // 300ms通常是合适的
        'calculation' => [
            // 避免过于复杂的公式
            'formula' => 'field1_sum + field2_sum',  // 简单公式
            // 'formula' => 'field1_sum * field2_sum / field3_avg + field4_max - field5_min',  // 避免过于复杂
        ],
    ],
];
```

### 3. 错误处理

```php
<?php
// 设置合理的默认值
$safeField = [
    'config' => [
        'calculation' => [
            'type' => 'sum',
            'sourceFields' => ['amount'],
            'defaultValue' => 0,        // 设置默认值
            'precision' => 2,           // 控制精度
        ],
    ],
];
```

## 常见问题解答

### Q: 计算字段支持嵌套依赖吗？

A: 目前不支持计算字段之间的依赖关系，只支持依赖表格数据字段。

### Q: 如何处理空值或非数字值？

A: 系统会自动将空值和非数字值转换为0进行计算。

### Q: 过滤条件支持OR逻辑吗？

A: 目前只支持AND逻辑，所有过滤条件必须同时满足。

### Q: 自定义公式有长度限制吗？

A: 建议公式长度不超过200个字符，过长的公式可能影响性能。

### Q: 计算字段的值会保存到数据库吗？

A: 计算字段的值通常不保存到数据库，而是实时计算。如需保存，可在后端处理时手动保存计算结果。
