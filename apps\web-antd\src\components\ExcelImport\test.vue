<template>
  <div class="excel-import-test">
    <h2>Excel导入组件测试</h2>
    
    <div class="test-section">
      <h3>基本功能测试</h3>
      <Button type="primary" @click="openBasicImport">
        打开基本导入
      </Button>
      
      <ExcelImport
        ref="basicImportRef"
        @success="handleBasicSuccess"
        @error="handleBasicError"
      />
    </div>

    <div class="test-section">
      <h3>带验证的导入测试</h3>
      <Button type="primary" @click="openValidatedImport">
        打开验证导入
      </Button>
      
      <ExcelImport
        ref="validatedImportRef"
        :validate-data="validateData"
        :transform-data="transformData"
        @success="handleValidatedSuccess"
        @error="handleValidatedError"
      />
    </div>

    <div class="test-section">
      <h3>导入结果</h3>
      <div v-if="importResults.length > 0">
        <h4>最近导入的数据：</h4>
        <pre>{{ JSON.stringify(importResults, null, 2) }}</pre>
      </div>
      <div v-else>
        <p>暂无导入数据</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

import { Button, message } from 'ant-design-vue';

import { ExcelImport } from './index';
import type { ValidationResult } from './types';

const basicImportRef = ref();
const validatedImportRef = ref();
const importResults = ref<any[]>([]);

// 基本导入测试
const openBasicImport = () => {
  basicImportRef.value?.open();
};

const handleBasicSuccess = (data: any[]) => {
  console.log('基本导入成功:', data);
  importResults.value = data;
  message.success(`基本导入成功，共 ${data.length} 条数据`);
};

const handleBasicError = (error: string) => {
  console.error('基本导入失败:', error);
  message.error(`基本导入失败: ${error}`);
};

// 带验证的导入测试
const openValidatedImport = () => {
  validatedImportRef.value?.open();
};

const validateData = (data: any[]): ValidationResult => {
  const errors: string[] = [];
  
  if (data.length === 0) {
    errors.push('没有数据');
    return { valid: false, errors };
  }

  data.forEach((row, index) => {
    if (!row[0]) {
      errors.push(`第${index + 1}行第1列不能为空`);
    }
    if (!row[1]) {
      errors.push(`第${index + 1}行第2列不能为空`);
    }
  });

  return {
    valid: errors.length === 0,
    errors,
  };
};

const transformData = (data: any[]) => {
  return data.map((row, index) => ({
    id: index + 1,
    col1: row[0],
    col2: row[1],
    col3: row[2],
    timestamp: new Date().toISOString(),
  }));
};

const handleValidatedSuccess = (data: any[]) => {
  console.log('验证导入成功:', data);
  importResults.value = data;
  message.success(`验证导入成功，共 ${data.length} 条数据`);
};

const handleValidatedError = (error: string) => {
  console.error('验证导入失败:', error);
  message.error(`验证导入失败: ${error}`);
};
</script>

<style scoped>
.excel-import-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
}

.test-section h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #1890ff;
}

.test-section h4 {
  margin-top: 16px;
  margin-bottom: 8px;
}

pre {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}
</style>
