# EditTable 导入功能使用指南

## 概述

EditTable 组件现在支持基于表格列配置的智能导入功能，可以自动根据表格的列定义来验证和转换导入的数据，并支持生成对应的导入模板。

## 功能特性

### 1. 智能数据映射
- **优先使用表格列配置**：导入时优先使用 `params.columns` 配置进行数据映射
- **回退到表单配置**：如果没有列配置，则使用 `params.form` 配置
- **自动过滤特殊列**：自动排除操作列（action/actions）等不可导入的列

### 2. 数据验证
- **基于列配置验证**：根据列的 `required` 属性进行必填验证
- **类型验证**：支持不同数据类型的格式验证
- **自定义验证**：支持传入自定义验证函数

### 3. 模板生成
- **自动生成模板**：基于表格列配置自动生成 Excel 导入模板
- **示例数据**：模板包含示例数据，帮助用户理解数据格式
- **一键下载**：支持直接下载 Excel 模板文件

### 4. 下拉菜单按钮
- **明细文件按钮**：将导入相关功能整合到一个下拉菜单中
- **导入明细**：打开导入弹窗
- **下载模板**：直接下载导入模板

## 使用方法

### 1. 基本配置

```typescript
const tableParams = {
  // 表格列配置
  columns: [
    {
      field: 'name',
      title: '产品名称',
      width: 150,
      type: 'text',
      required: true, // 标记为必填字段
    },
    {
      field: 'price',
      title: '价格',
      width: 100,
      type: 'number',
      required: true,
    },
    // ... 其他列配置
  ],
  
  // 工具栏按钮配置（支持下拉菜单）
  tablist: [
    {
      title: '明细文件',
      type: 'import',
      key: 'import_dropdown',
      icon: 'import',
      children: [
        {
          title: '导入明细',
          type: 'import',
          key: 'import',
          icon: 'import',
        },
        {
          title: '下载模板',
          type: 'download_template',
          key: 'download_template',
          icon: 'download_template',
        },
      ],
    },
  ],
};
```

### 2. 在模板中使用

```vue
<template>
  <EditTable
    ref="editTableRef"
    v-model="tableData"
    :params="tableParams"
  />
</template>
```

### 3. 列配置说明

#### 支持的列类型
- `text`: 文本类型
- `number`: 数字类型
- `date`: 日期类型
- `email`: 邮箱类型
- `phone`: 电话类型

#### 列配置属性
- `field`: 字段名（必填）
- `title`: 列标题（必填）
- `type`: 列类型
- `required`: 是否必填（用于导入验证）
- `width`: 列宽度
- `align`: 对齐方式

### 4. 导入流程

1. **点击"明细文件"按钮**：显示下拉菜单
2. **选择"下载模板"**：下载基于列配置生成的 Excel 模板
3. **填写模板数据**：按照模板格式填写要导入的数据
4. **选择"导入明细"**：打开导入弹窗，上传填写好的文件
5. **数据验证**：系统自动验证数据格式和必填字段
6. **导入完成**：验证通过后数据自动添加到表格中

## 数据映射规则

### 1. 列配置映射
当使用表格列配置时，数据映射规则如下：
- Excel 第1列 → 第1个可导入列的 field
- Excel 第2列 → 第2个可导入列的 field
- 以此类推...

### 2. 可导入列过滤
以下类型的列会被自动排除：
- `type` 为 `action` 或 `actions` 的列
- `field` 为 `action` 或 `actions` 的列

### 3. 示例数据生成
模板中的示例数据根据列类型自动生成：
- `text`: "示例{列标题}"
- `number`: "100"
- `date`: "2024-01-01"
- `email`: "<EMAIL>"
- `phone`: "13800138000"

## 高级功能

### 1. 自定义验证
```typescript
const validateImportData = (data: any[]) => {
  const errors: string[] = [];
  
  data.forEach((row, index) => {
    // 自定义验证逻辑
    if (row[0] && row[0].length > 50) {
      errors.push(`第${index + 1}行：产品名称不能超过50个字符`);
    }
  });
  
  return {
    valid: errors.length === 0,
    errors,
  };
};
```

### 2. 自定义数据转换
```typescript
const transformImportData = (data: any[]) => {
  return data.map((row, index) => ({
    // 自定义转换逻辑
    name: row[0]?.trim(),
    price: parseFloat(row[1]) || 0,
    // ...
  }));
};
```

## 注意事项

1. **列顺序**：导入时严格按照列配置的顺序进行映射
2. **数据类型**：确保 Excel 中的数据类型与列配置匹配
3. **必填验证**：标记为 `required: true` 的列在导入时不能为空
4. **特殊字符**：避免在数据中使用特殊字符，可能影响导入解析

## 示例文件

完整的使用示例请参考：`apps/web-antd/src/views/examples/edittable-import-example.vue`
