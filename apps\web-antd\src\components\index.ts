// 全局组件注册
import type { App } from 'vue';

import DetailDescriptions from './detail-descriptions';
import ExcelImport from './ExcelImport';
import GeneralTabs from './general-tabs';
import ToolsButton from './tools-button';

// 导出组件
export { DetailDescriptions, ExcelImport, GeneralTabs, ToolsButton };

// 导出类型
export type {
  ComponentType,
  DescriptionItem,
  DescriptionRule,
  FileInfo,
  ImageInfo,
  LinkInfo,
  OptionConfig,
  OptionItem,
} from './detail-descriptions';

export type {
  ExcelImportProps,
  ValidationResult,
  ImportResult,
  ExcelSheetData,
  ExcelImportEvents,
  ExcelImportInstance,
} from './ExcelImport';

export type {
  ComponentMap,
  PresetComponentType,
  PresetTabConfig,
  TabItem,
  TabsConfig,
  TabsEvents,
} from './general-tabs';

export { default as GroupTitle } from './group-title';

// 全局注册函数
export function registerGlobalComponents(app: App) {
  app.component('DetailDescriptions', DetailDescriptions);
  app.component('ExcelImport', ExcelImport);
  app.component('GeneralTabs', GeneralTabs);
  app.component('ToolsButton', ToolsButton);
}

// 默认导出
export default {
  install: registerGlobalComponents,
};

export type { ToolsButtonItem } from './tools-button';
