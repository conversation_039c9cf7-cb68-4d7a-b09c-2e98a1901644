/**
 * 计算字段精度问题演示
 * 展示JavaScript浮点数精度问题及其解决方案
 */

// 模拟导入计算引擎（实际使用时从正确路径导入）
// import { calculationEngine } from '#/utils/calculation/calculation-engine';

console.log('=== JavaScript 浮点数精度问题演示 ===\n');

// 1. 经典的JavaScript浮点数精度问题
console.log('1. 经典精度问题：');
console.log('0.1 + 0.2 =', 0.1 + 0.2); // 输出: 0.30000000000000004
console.log('0.1 + 0.2 === 0.3:', 0.1 + 0.2 === 0.3); // 输出: false
console.log('');

// 2. 更多精度问题示例
console.log('2. 更多精度问题：');
console.log('0.1 * 3 =', 0.1 * 3); // 输出: 0.30000000000000004
console.log('0.3 - 0.1 =', 0.3 - 0.1); // 输出: 0.19999999999999998
console.log('1.4 * 3 =', 1.4 * 3); // 输出: 4.199999999999999
console.log('');

// 3. 在表格计算中的实际问题
console.log('3. 表格计算中的问题：');
const tableData = [
  { price: 0.1, quantity: 3 },
  { price: 0.2, quantity: 2 },
  { price: 0.3, quantity: 1 },
];

// 使用原生JavaScript计算总价
const nativeSum = tableData.reduce((sum, item) => sum + item.price, 0);
console.log('原生JavaScript求和:', nativeSum); // 可能输出: 0.6000000000000001

// 使用原生JavaScript计算平均价格
const nativeAverage = nativeSum / tableData.length;
console.log('原生JavaScript平均值:', nativeAverage); // 可能输出: 0.20000000000000004
console.log('');

// 4. 我们的解决方案演示（伪代码，实际需要导入真实的计算引擎）
console.log('4. 使用计算引擎的精确计算：');

// 模拟计算引擎的精确计算结果
function simulateCalculationEngine() {
  // 求和配置
  const sumConfig = {
    targetField: 'total_price',
    rule: {
      type: 'sum',
      sourceFields: ['price'],
      precision: 2,
      defaultValue: 0,
    },
  };

  // 平均值配置
  const avgConfig = {
    targetField: 'average_price',
    rule: {
      type: 'average',
      sourceFields: ['price'],
      precision: 2,
      defaultValue: 0,
    },
  };

  // 模拟精确计算结果
  console.log('计算引擎求和结果:', 0.6); // 精确的 0.6
  console.log('计算引擎平均值结果:', 0.2); // 精确的 0.2
}

simulateCalculationEngine();
console.log('');

// 5. 精度处理的核心原理
console.log('5. 精度处理原理演示：');

function roundToPrecision(num, precision) {
  if (isNaN(num) || !isFinite(num)) {
    return 0;
  }
  
  // 使用 Number.EPSILON 处理浮点数精度问题
  const factor = Math.pow(10, precision);
  return Math.round((num + Number.EPSILON) * factor) / factor;
}

function safeAdd(a, b) {
  const getDecimalPlaces = (num) => {
    if (Math.floor(num) === num) return 0;
    const str = num.toString();
    if (str.indexOf('.') !== -1 && str.indexOf('e-') === -1) {
      return str.split('.')[1].length;
    }
    return 0;
  };

  const precision = Math.max(getDecimalPlaces(a), getDecimalPlaces(b));
  const factor = Math.pow(10, precision);
  return (Math.round(a * factor) + Math.round(b * factor)) / factor;
}

console.log('原生加法: 0.1 + 0.2 =', 0.1 + 0.2);
console.log('安全加法: safeAdd(0.1, 0.2) =', safeAdd(0.1, 0.2));
console.log('精度处理: roundToPrecision(0.1 + 0.2, 1) =', roundToPrecision(0.1 + 0.2, 1));
console.log('');

// 6. 实际业务场景示例
console.log('6. 实际业务场景：');

const orderItems = [
  { name: '商品A', price: 12.34, quantity: 2, discount: 0.05 },
  { name: '商品B', price: 56.78, quantity: 1, discount: 0.1 },
  { name: '商品C', price: 9.99, quantity: 3, discount: 0.15 },
];

console.log('订单明细:');
orderItems.forEach((item, index) => {
  console.log(`  ${index + 1}. ${item.name}: ¥${item.price} × ${item.quantity}, 折扣: ${item.discount * 100}%`);
});

// 原生计算
const nativeTotalPrice = orderItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
const nativeTotalDiscount = orderItems.reduce((sum, item) => sum + (item.price * item.quantity * item.discount), 0);
const nativeFinalPrice = nativeTotalPrice - nativeTotalDiscount;

console.log('\n原生JavaScript计算结果:');
console.log('  总价:', nativeTotalPrice);
console.log('  总折扣:', nativeTotalDiscount);
console.log('  最终价格:', nativeFinalPrice);

// 使用我们的精确计算方法
const safeTotalPrice = orderItems.reduce((sum, item) => safeAdd(sum, item.price * item.quantity), 0);
const safeTotalDiscount = orderItems.reduce((sum, item) => safeAdd(sum, item.price * item.quantity * item.discount), 0);
const safeFinalPrice = roundToPrecision(safeTotalPrice - safeTotalDiscount, 2);

console.log('\n精确计算结果:');
console.log('  总价:', roundToPrecision(safeTotalPrice, 2));
console.log('  总折扣:', roundToPrecision(safeTotalDiscount, 2));
console.log('  最终价格:', safeFinalPrice);
console.log('');

// 7. 计算字段配置示例
console.log('7. 计算字段配置示例：');

const calculationFieldExamples = {
  // 订单总金额
  totalAmount: {
    field: 'total_amount',
    title: '订单总金额',
    type: 'calculated',
    config: {
      calculation: {
        type: 'sum',
        sourceFields: ['amount'],
        precision: 2,
        defaultValue: 0,
      },
    },
  },

  // 平均单价
  averagePrice: {
    field: 'average_price',
    title: '平均单价',
    type: 'calculated',
    config: {
      calculation: {
        type: 'average',
        sourceFields: ['unit_price'],
        precision: 2,
        defaultValue: 0,
      },
    },
  },

  // 利润率（自定义公式）
  profitMargin: {
    field: 'profit_margin',
    title: '利润率',
    type: 'calculated',
    config: {
      calculation: {
        type: 'formula',
        sourceFields: ['revenue', 'cost'],
        formula: '(field1_sum - field2_sum) / field1_sum * 100',
        precision: 2,
        defaultValue: 0,
      },
    },
  },
};

console.log('计算字段配置示例:');
Object.entries(calculationFieldExamples).forEach(([key, config]) => {
  console.log(`  ${key}:`, JSON.stringify(config, null, 2));
});

console.log('\n=== 演示完成 ===');
console.log('\n总结:');
console.log('1. JavaScript原生浮点数运算存在精度问题');
console.log('2. 我们的计算引擎使用精确的数学运算方法');
console.log('3. 支持配置小数位数，自动处理精度');
console.log('4. 适用于财务、统计等对精度要求高的场景');
