<template>
  <div class="calculation-fields-example">
    <h2>计算字段使用示例</h2>

    <!-- 表单区域 -->
    <div class="form-section">
      <h3>订单信息表单</h3>
      <VbenForm
        ref="formRef"
        :schema="formSchema"
        @submit="handleSubmit"
        @values-change="handleValuesChange"
      />
    </div>

    <!-- 调试信息 -->
    <div class="debug-section" v-if="showDebug">
      <h3>调试信息</h3>
      <pre>{{ debugInfo }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';

import { VbenForm } from '#/adapter/form';
import { transformBackendSearchToSchema } from '#/utils/search-schema';
import { useFormCalculationIntegration } from '#/utils/calculation/form-calculation-integration';

// 表单引用
const formRef = ref();
const showDebug = ref(false);

// 后端返回的表单配置数据（模拟）
const backendFormConfig = [
  // 基础信息
  {
    field: 'order_no',
    title: '订单号',
    type: 'input',
    required: true,
    config: {
      placeholder: '请输入订单号',
    },
  },
  {
    field: 'customer_name',
    title: '客户名称',
    type: 'input',
    required: true,
    config: {
      placeholder: '请输入客户名称',
    },
  },

  // 订单明细表格
  {
    field: 'order_items',
    title: '订单明细',
    type: 'edittable',
    config: {
      columns: [
        {
          field: 'product_name',
          title: '商品名称',
          type: 'input',
          width: 200,
        },
        {
          field: 'quantity',
          title: '数量',
          type: 'number',
          width: 100,
        },
        {
          field: 'unit_price',
          title: '单价',
          type: 'number',
          width: 120,
        },
        {
          field: 'discount',
          title: '折扣',
          type: 'number',
          width: 100,
        },
        {
          field: 'amount',
          title: '小计',
          type: 'number',
          width: 120,
        },
      ],
      tabList: [
        {
          product_name: '商品A',
          quantity: 2,
          unit_price: 100,
          discount: 0.1,
          amount: 180,
        },
        {
          product_name: '商品B',
          quantity: 1,
          unit_price: 200,
          discount: 0,
          amount: 200,
        },
        {
          product_name: '商品C',
          quantity: 3,
          unit_price: 50,
          discount: 0.05,
          amount: 142.5,
        },
      ],
    },
  },

  // 计算字段：订单总金额
  {
    field: 'total_amount',
    title: '订单总金额',
    type: 'calculated',
    config: {
      placeholder: '订单总金额（自动计算）',
      calculation: {
        type: 'sum',
        sourceFields: ['amount'],
        precision: 2,
        defaultValue: 0,
      },
    },
  },

  // 计算字段：商品总数量
  {
    field: 'total_quantity',
    title: '商品总数量',
    type: 'calculated',
    config: {
      placeholder: '商品总数量（自动计算）',
      calculation: {
        type: 'sum',
        sourceFields: ['quantity'],
        precision: 0,
        defaultValue: 0,
      },
    },
  },

  // 计算字段：平均单价
  {
    field: 'average_price',
    title: '平均单价',
    type: 'calculated',
    config: {
      placeholder: '平均单价（自动计算）',
      calculation: {
        type: 'average',
        sourceFields: ['unit_price'],
        precision: 2,
        defaultValue: 0,
      },
    },
  },

  // 计算字段：最高单价
  {
    field: 'max_price',
    title: '最高单价',
    type: 'calculated',
    config: {
      placeholder: '最高单价（自动计算）',
      calculation: {
        type: 'max',
        sourceFields: ['unit_price'],
        precision: 2,
        defaultValue: 0,
      },
    },
  },

  // 计算字段：商品种类数
  {
    field: 'product_count',
    title: '商品种类数',
    type: 'calculated',
    config: {
      placeholder: '商品种类数（自动计算）',
      calculation: {
        type: 'count',
        sourceFields: ['product_name'],
        defaultValue: 0,
      },
    },
  },

  // 计算字段：总折扣金额（自定义公式）
  {
    field: 'total_discount',
    title: '总折扣金额',
    type: 'calculated',
    config: {
      placeholder: '总折扣金额（自动计算）',
      calculation: {
        type: 'formula',
        sourceFields: ['unit_price', 'quantity', 'discount'],
        formula: 'field1_sum * field2_sum * field3_avg',
        precision: 2,
        defaultValue: 0,
      },
    },
  },

  // 计算字段：有折扣商品数量（带过滤条件）
  {
    field: 'discounted_items_count',
    title: '有折扣商品数量',
    type: 'calculated',
    config: {
      placeholder: '有折扣商品数量（自动计算）',
      calculation: {
        type: 'count',
        sourceFields: ['product_name'],
        filter: [
          {
            field: 'discount',
            operator: '>',
            value: 0,
          },
        ],
        defaultValue: 0,
      },
    },
  },

  // 其他字段
  {
    field: 'remark',
    title: '备注',
    type: 'textarea',
    config: {
      placeholder: '请输入备注信息',
      rows: 3,
    },
  },
];

// 转换后的表单Schema
const formSchema = computed(() => {
  return transformBackendSearchToSchema(backendFormConfig);
});

// 计算字段集成器
let calculationIntegration: any = null;

// 表单提交处理
const handleSubmit = (values: any) => {
  console.log('表单提交:', values);
  alert('表单提交成功！请查看控制台输出。');
};

// 表单值变化处理
const handleValuesChange = (values: any, changedFields: string[]) => {
  console.log('表单值变化:', { values, changedFields });

  // 如果表格数据发生变化，触发重新计算
  if (changedFields.includes('order_items')) {
    calculationIntegration?.recalculate(values.order_items);
  }
};

// 调试信息
const debugInfo = computed(() => {
  if (!calculationIntegration) return {};

  return {
    registeredFields: calculationIntegration.getRegisteredFields(),
    debugInfo: calculationIntegration.getDebugInfo(),
  };
});

// 组件挂载
onMounted(async () => {
  // 等待表单初始化
  await new Promise((resolve) => setTimeout(resolve, 100));

  if (formRef.value?.formApi) {
    // 创建计算字段集成器
    calculationIntegration = useFormCalculationIntegration(
      formRef.value.formApi,
    ).integration;

    // 扫描并注册计算字段
    calculationIntegration.scanAndRegisterCalculationFields(formSchema.value);

    console.log('计算字段集成完成:', calculationIntegration.getDebugInfo());
  }
});

// 组件卸载
onUnmounted(() => {
  if (calculationIntegration) {
    calculationIntegration.unregisterAllCalculationFields();
  }
});

// 切换调试信息显示
const toggleDebug = () => {
  showDebug.value = !showDebug.value;
};

// 暴露方法供外部调用
defineExpose({
  toggleDebug,
  getDebugInfo: () => debugInfo.value,
  recalculateFields: () => {
    const formValues = formRef.value?.formApi?.getValues();
    calculationIntegration?.recalculate(formValues?.order_items);
  },
});

/*
使用说明：

1. 在表单配置中添加计算字段：
   - type: 'calculated'
   - config.calculation: 计算配置对象

2. 支持的计算类型：
   - sum: 求和
   - average: 平均值
   - product: 乘积
   - max: 最大值
   - min: 最小值
   - count: 计数
   - formula: 自定义公式

3. 计算字段会自动监听表格数据变化并重新计算

4. 可以通过过滤条件只对满足条件的数据进行计算

5. 支持设置小数位数和默认值

示例配置：
{
  field: 'total_amount',
  title: '总金额',
  type: 'calculated',
  config: {
    calculation: {
      type: 'sum',
      sourceFields: ['amount'],
      precision: 2,
      defaultValue: 0,
    },
  },
}
*/
</script>

<style scoped>
.calculation-fields-example {
  max-width: 1200px;
  padding: 20px;
  margin: 0 auto;
}

.form-section {
  padding: 20px;
  margin-bottom: 30px;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
}

.debug-section {
  padding: 20px;
  background: #f9f9f9;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
}

.debug-section pre {
  max-height: 400px;
  padding: 10px;
  overflow: auto;
  background: #f5f5f5;
  border-radius: 4px;
}

h2 {
  margin-bottom: 20px;
  color: #1890ff;
}

h3 {
  margin-bottom: 15px;
  color: #333;
}
</style>
