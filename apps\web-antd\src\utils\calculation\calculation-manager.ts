/**
 * 计算字段管理器
 * 负责管理表格数据变化监听和计算字段的自动更新
 */

import type { CalculationConfig } from './calculation-engine';

import { calculationEngine } from './calculation-engine';

export interface CalculationFieldInfo {
  /** 计算配置 */
  config: CalculationConfig;
  /** 表单API引用 */
  formApi?: any;
  /** 表格数据引用 */
  tableDataRef?: any;
}

/**
 * 计算字段管理器类
 */
export class CalculationManager {
  private static instance: CalculationManager;
  private calculationFields: Map<string, CalculationFieldInfo> = new Map();
  private tableDataWatchers: Map<string, any> = new Map();

  static getInstance(): CalculationManager {
    if (!CalculationManager.instance) {
      CalculationManager.instance = new CalculationManager();
    }
    return CalculationManager.instance;
  }

  /**
   * 注册计算字段
   * @param fieldName 字段名
   * @param config 计算配置
   * @param formApi 表单API
   * @param tableDataRef 表格数据引用
   */
  registerCalculationField(
    fieldName: string,
    config: CalculationConfig,
    formApi?: any,
    tableDataRef?: any,
  ): void {
    console.log(`[计算字段管理器] 注册计算字段: ${fieldName}`, {
      config,
      hasFormApi: !!formApi,
      hasTableDataRef: !!tableDataRef,
    });

    this.calculationFields.set(fieldName, {
      config,
      formApi,
      tableDataRef,
    });

    // 如果有表格数据引用，设置监听器
    if (tableDataRef) {
      this.setupTableDataWatcher(fieldName, tableDataRef);
    }

    // 立即执行一次计算
    this.calculateField(fieldName);
  }

  /**
   * 注销计算字段
   * @param fieldName 字段名
   */
  unregisterCalculationField(fieldName: string): void {
    console.log(`[计算字段管理器] 注销计算字段: ${fieldName}`);

    // 清理监听器
    const watcher = this.tableDataWatchers.get(fieldName);
    if (watcher && typeof watcher === 'function') {
      watcher(); // 调用停止监听函数
    }
    this.tableDataWatchers.delete(fieldName);

    // 移除字段信息
    this.calculationFields.delete(fieldName);
  }

  /**
   * 设置表格数据监听器
   * @param fieldName 字段名
   * @param tableDataRef 表格数据引用
   */
  private setupTableDataWatcher(fieldName: string, tableDataRef: any): void {
    // 如果已经有监听器，先清理
    const existingWatcher = this.tableDataWatchers.get(fieldName);
    if (existingWatcher && typeof existingWatcher === 'function') {
      existingWatcher();
    }

    // 创建新的监听器（这里需要根据实际的响应式系统来实现）
    // 由于我们使用的是Vue的响应式系统，这里使用watch
    import('vue').then(({ watch }) => {
      const stopWatcher = watch(
        () => tableDataRef.value,
        (newData) => {
          console.log(`[计算字段管理器] 表格数据变化，重新计算字段: ${fieldName}`);
          this.calculateField(fieldName, newData);
        },
        { deep: true, immediate: false }
      );

      this.tableDataWatchers.set(fieldName, stopWatcher);
    });
  }

  /**
   * 计算指定字段
   * @param fieldName 字段名
   * @param tableData 表格数据（可选，如果不提供则从引用中获取）
   */
  private calculateField(fieldName: string, tableData?: any[]): void {
    const fieldInfo = this.calculationFields.get(fieldName);
    if (!fieldInfo) {
      console.warn(`[计算字段管理器] 未找到计算字段配置: ${fieldName}`);
      return;
    }

    const { config, formApi, tableDataRef } = fieldInfo;

    // 获取表格数据
    const data = tableData || (tableDataRef?.value) || [];
    
    if (!Array.isArray(data)) {
      console.warn(`[计算字段管理器] 表格数据不是数组格式: ${fieldName}`, data);
      return;
    }

    // 执行计算
    calculationEngine.calculateWithDebounce(
      data,
      config,
      (result) => {
        console.log(`[计算字段管理器] 计算完成: ${fieldName} = ${result}`);
        
        // 更新表单字段值
        if (formApi && formApi.setFieldValue) {
          try {
            formApi.setFieldValue(fieldName, result);
          } catch (error) {
            console.warn(`[计算字段管理器] 更新字段值失败: ${fieldName}`, error);
          }
        }
      }
    );
  }

  /**
   * 手动触发所有计算字段重新计算
   * @param tableData 表格数据
   */
  recalculateAllFields(tableData: any[]): void {
    console.log(`[计算字段管理器] 手动触发所有计算字段重新计算`, {
      fieldCount: this.calculationFields.size,
      dataLength: tableData?.length || 0,
    });

    this.calculationFields.forEach((fieldInfo, fieldName) => {
      this.calculateField(fieldName, tableData);
    });
  }

  /**
   * 手动触发指定字段重新计算
   * @param fieldName 字段名
   * @param tableData 表格数据
   */
  recalculateField(fieldName: string, tableData: any[]): void {
    console.log(`[计算字段管理器] 手动触发字段重新计算: ${fieldName}`);
    this.calculateField(fieldName, tableData);
  }

  /**
   * 获取所有已注册的计算字段
   */
  getRegisteredFields(): string[] {
    return Array.from(this.calculationFields.keys());
  }

  /**
   * 检查字段是否已注册
   * @param fieldName 字段名
   */
  isFieldRegistered(fieldName: string): boolean {
    return this.calculationFields.has(fieldName);
  }

  /**
   * 清理所有计算字段
   */
  clearAllFields(): void {
    console.log(`[计算字段管理器] 清理所有计算字段`);

    // 清理所有监听器
    this.tableDataWatchers.forEach((watcher) => {
      if (typeof watcher === 'function') {
        watcher();
      }
    });

    // 清理所有数据
    this.calculationFields.clear();
    this.tableDataWatchers.clear();
    
    // 清理计算引擎的防抖定时器
    calculationEngine.clearDebounceTimers();
  }

  /**
   * 获取调试信息
   */
  getDebugInfo(): any {
    return {
      registeredFields: this.getRegisteredFields(),
      fieldCount: this.calculationFields.size,
      watcherCount: this.tableDataWatchers.size,
      fieldsDetail: Array.from(this.calculationFields.entries()).map(([name, info]) => ({
        name,
        config: info.config,
        hasFormApi: !!info.formApi,
        hasTableDataRef: !!info.tableDataRef,
      })),
    };
  }
}

// 导出单例实例
export const calculationManager = CalculationManager.getInstance();

/**
 * 便捷函数：注册计算字段
 */
export function registerCalculationField(
  fieldName: string,
  config: CalculationConfig,
  formApi?: any,
  tableDataRef?: any,
): void {
  calculationManager.registerCalculationField(fieldName, config, formApi, tableDataRef);
}

/**
 * 便捷函数：注销计算字段
 */
export function unregisterCalculationField(fieldName: string): void {
  calculationManager.unregisterCalculationField(fieldName);
}

/**
 * 便捷函数：手动触发重新计算
 */
export function recalculateFields(tableData: any[], fieldNames?: string[]): void {
  if (fieldNames && fieldNames.length > 0) {
    fieldNames.forEach(fieldName => {
      calculationManager.recalculateField(fieldName, tableData);
    });
  } else {
    calculationManager.recalculateAllFields(tableData);
  }
}
