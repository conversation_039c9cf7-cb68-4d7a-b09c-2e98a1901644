/**
 * 表单计算字段集成工具
 * 负责在表单中自动识别和注册计算字段
 */

import type { VbenFormSchema } from '#/adapter/form';
import type { CalculationConfig } from './calculation-engine';

import { calculationManager } from './calculation-manager';

/**
 * 表单计算字段集成器
 */
export class FormCalculationIntegration {
  private formApi: any;
  private editTableRef: any;
  private registeredFields: Set<string> = new Set();

  constructor(formApi: any, editTableRef?: any) {
    this.formApi = formApi;
    this.editTableRef = editTableRef;
  }

  /**
   * 扫描表单Schema并自动注册计算字段
   * @param schema 表单Schema数组
   */
  scanAndRegisterCalculationFields(schema: VbenFormSchema[]): void {
    console.log('[表单计算集成] 开始扫描计算字段', { schemaCount: schema.length });

    schema.forEach((field) => {
      if (this.isCalculationField(field)) {
        this.registerCalculationField(field);
      }
    });

    console.log('[表单计算集成] 计算字段扫描完成', {
      registeredCount: this.registeredFields.size,
      registeredFields: Array.from(this.registeredFields),
    });
  }

  /**
   * 判断字段是否为计算字段
   * @param field 表单字段Schema
   */
  private isCalculationField(field: VbenFormSchema): boolean {
    const componentProps = field.componentProps;
    
    // 检查是否有计算配置
    if (componentProps && typeof componentProps === 'object') {
      return !!(componentProps as any).calculationConfig;
    }

    // 检查是否为函数形式的componentProps
    if (typeof componentProps === 'function') {
      // 对于函数形式的componentProps，我们需要在运行时检查
      // 这里先返回false，在实际使用时再处理
      return false;
    }

    return false;
  }

  /**
   * 注册单个计算字段
   * @param field 表单字段Schema
   */
  private registerCalculationField(field: VbenFormSchema): void {
    const fieldName = field.fieldName;
    const componentProps = field.componentProps as any;
    const calculationConfig = componentProps?.calculationConfig as CalculationConfig;

    if (!calculationConfig) {
      console.warn(`[表单计算集成] 字段 ${fieldName} 缺少计算配置`);
      return;
    }

    console.log(`[表单计算集成] 注册计算字段: ${fieldName}`, calculationConfig);

    // 如果有EditTable引用，使用EditTable的注册方法
    if (this.editTableRef?.registerCalculationField) {
      this.editTableRef.registerCalculationField(fieldName, calculationConfig, this.formApi);
    } else {
      // 否则直接使用计算管理器
      calculationManager.registerCalculationField(fieldName, calculationConfig, this.formApi);
    }

    this.registeredFields.add(fieldName);
  }

  /**
   * 注销所有已注册的计算字段
   */
  unregisterAllCalculationFields(): void {
    console.log('[表单计算集成] 注销所有计算字段', {
      count: this.registeredFields.size,
      fields: Array.from(this.registeredFields),
    });

    this.registeredFields.forEach((fieldName) => {
      if (this.editTableRef?.unregisterCalculationField) {
        this.editTableRef.unregisterCalculationField(fieldName);
      } else {
        calculationManager.unregisterCalculationField(fieldName);
      }
    });

    this.registeredFields.clear();
  }

  /**
   * 手动触发重新计算
   * @param tableData 表格数据
   * @param fieldNames 指定字段名（可选）
   */
  recalculateFields(tableData?: any[], fieldNames?: string[]): void {
    console.log('[表单计算集成] 手动触发重新计算', {
      hasTableData: !!tableData,
      dataLength: tableData?.length || 0,
      fieldNames,
    });

    if (this.editTableRef?.recalculateFields) {
      this.editTableRef.recalculateFields(fieldNames);
    } else if (tableData) {
      if (fieldNames && fieldNames.length > 0) {
        fieldNames.forEach(fieldName => {
          calculationManager.recalculateField(fieldName, tableData);
        });
      } else {
        calculationManager.recalculateAllFields(tableData);
      }
    }
  }

  /**
   * 获取已注册的计算字段列表
   */
  getRegisteredFields(): string[] {
    return Array.from(this.registeredFields);
  }

  /**
   * 检查字段是否已注册
   * @param fieldName 字段名
   */
  isFieldRegistered(fieldName: string): boolean {
    return this.registeredFields.has(fieldName);
  }

  /**
   * 更新EditTable引用
   * @param editTableRef EditTable组件引用
   */
  updateEditTableRef(editTableRef: any): void {
    this.editTableRef = editTableRef;
  }

  /**
   * 获取调试信息
   */
  getDebugInfo(): any {
    return {
      hasFormApi: !!this.formApi,
      hasEditTableRef: !!this.editTableRef,
      registeredFieldsCount: this.registeredFields.size,
      registeredFields: Array.from(this.registeredFields),
      calculationManagerInfo: calculationManager.getDebugInfo(),
    };
  }
}

/**
 * 创建表单计算字段集成器
 * @param formApi 表单API
 * @param editTableRef EditTable组件引用（可选）
 */
export function createFormCalculationIntegration(
  formApi: any,
  editTableRef?: any,
): FormCalculationIntegration {
  return new FormCalculationIntegration(formApi, editTableRef);
}

/**
 * 便捷函数：自动扫描并注册表单中的计算字段
 * @param schema 表单Schema
 * @param formApi 表单API
 * @param editTableRef EditTable组件引用（可选）
 */
export function autoRegisterCalculationFields(
  schema: VbenFormSchema[],
  formApi: any,
  editTableRef?: any,
): FormCalculationIntegration {
  const integration = createFormCalculationIntegration(formApi, editTableRef);
  integration.scanAndRegisterCalculationFields(schema);
  return integration;
}

/**
 * 便捷函数：在组件卸载时清理计算字段
 * @param integration 表单计算集成器实例
 */
export function cleanupCalculationFields(integration: FormCalculationIntegration): void {
  if (integration) {
    integration.unregisterAllCalculationFields();
  }
}

/**
 * Vue组合式函数：使用计算字段集成
 * @param formApi 表单API
 * @param editTableRef EditTable组件引用（可选）
 */
export function useFormCalculationIntegration(formApi: any, editTableRef?: any) {
  const integration = createFormCalculationIntegration(formApi, editTableRef);

  // 在组件卸载时自动清理
  import('vue').then(({ onUnmounted }) => {
    onUnmounted(() => {
      integration.unregisterAllCalculationFields();
    });
  });

  return {
    integration,
    scanAndRegister: (schema: VbenFormSchema[]) => integration.scanAndRegisterCalculationFields(schema),
    recalculate: (tableData?: any[], fieldNames?: string[]) => integration.recalculateFields(tableData, fieldNames),
    getRegisteredFields: () => integration.getRegisteredFields(),
    isFieldRegistered: (fieldName: string) => integration.isFieldRegistered(fieldName),
    updateEditTableRef: (ref: any) => integration.updateEditTableRef(ref),
    cleanup: () => integration.unregisterAllCalculationFields(),
    getDebugInfo: () => integration.getDebugInfo(),
  };
}
