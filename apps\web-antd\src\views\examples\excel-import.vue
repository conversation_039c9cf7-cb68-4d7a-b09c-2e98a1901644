<template>
  <div class="excel-import-example">
    <PageWrapper title="Excel导入组件示例">
      <template #content>
        <div class="mb-4">
          <Alert
            message="Excel导入组件演示"
            description="这是一个完整的Excel/CSV文件导入组件，支持文件上传、数据预览、验证和导入功能。支持多工作表切换、自定义数据验证和转换。"
            type="info"
            show-icon
            closable
          />
        </div>

        <Card title="用户数据导入" class="mb-6">
          <template #extra>
            <Space>
              <Button type="primary" @click="openImport">
                <UploadOutlined />
                导入数据
              </Button>
              <Button @click="downloadTemplate">
                <DownloadOutlined />
                下载模板
              </Button>
              <Button 
                @click="clearData" 
                :disabled="tableData.length === 0"
                danger
              >
                <DeleteOutlined />
                清空数据
              </Button>
            </Space>
          </template>

          <div class="mb-4">
            <Statistic
              title="当前数据总数"
              :value="tableData.length"
              suffix="条"
              :value-style="{ color: '#1890ff' }"
            />
          </div>

          <Table
            :columns="tableColumns"
            :data-source="tableData"
            :pagination="{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }"
            :scroll="{ x: 'max-content' }"
            size="small"
            bordered
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <Tag :color="record.status === '激活' ? 'green' : 'orange'">
                  {{ record.status }}
                </Tag>
              </template>
              <template v-else-if="column.key === 'action'">
                <Space>
                  <Button type="link" size="small" @click="editRecord(record)">
                    编辑
                  </Button>
                  <Button 
                    type="link" 
                    size="small" 
                    danger 
                    @click="deleteRecord(record)"
                  >
                    删除
                  </Button>
                </Space>
              </template>
            </template>

            <template #empty>
              <div class="empty-state">
                <InboxOutlined style="font-size: 48px; color: #ccc" />
                <p>暂无数据</p>
                <Button type="primary" @click="openImport">
                  立即导入数据
                </Button>
              </div>
            </template>
          </Table>
        </Card>

        <!-- Excel导入组件 -->
        <ExcelImport
          ref="excelImportRef"
          :max-size="10"
          :validate-data="validateImportData"
          :transform-data="transformImportData"
          @success="handleImportSuccess"
          @error="handleImportError"
        />
      </template>
    </PageWrapper>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

import {
  Alert,
  Button,
  Card,
  Space,
  Statistic,
  Table,
  Tag,
  message,
  Modal,
} from 'ant-design-vue';
import {
  DeleteOutlined,
  DownloadOutlined,
  InboxOutlined,
  UploadOutlined,
} from '@ant-design/icons-vue';

import { PageWrapper } from '@vben/common-ui';

import { ExcelImport } from '#/components';
import type { ValidationResult } from '#/components';

// 表格数据
const tableData = ref<any[]>([]);
const excelImportRef = ref();

// 表格列配置
const tableColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 80,
    align: 'center',
    fixed: 'left',
  },
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    width: 120,
    fixed: 'left',
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
    width: 200,
  },
  {
    title: '电话',
    dataIndex: 'phone',
    key: 'phone',
    width: 150,
  },
  {
    title: '部门',
    dataIndex: 'department',
    key: 'department',
    width: 120,
  },
  {
    title: '职位',
    dataIndex: 'position',
    key: 'position',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '导入时间',
    dataIndex: 'importTime',
    key: 'importTime',
    width: 180,
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right',
  },
];

// 打开导入弹窗
const openImport = () => {
  excelImportRef.value?.open();
};

// 下载模板
const downloadTemplate = () => {
  // 创建模板数据
  const templateData = [
    ['姓名', '邮箱', '电话', '部门', '职位'],
    ['张三', '<EMAIL>', '13800138000', '技术部', '工程师'],
    ['李四', '<EMAIL>', '13900139000', '市场部', '经理'],
    ['王五', '<EMAIL>', '13700137000', '人事部', '专员'],
  ];

  // 创建CSV内容
  const csvContent = templateData.map(row => 
    row.map(cell => `"${cell}"`).join(',')
  ).join('\n');

  // 创建下载链接
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', '用户导入模板.csv');
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  message.success('模板下载成功');
};

// 清空数据
const clearData = () => {
  Modal.confirm({
    title: '确认清空',
    content: '确定要清空所有数据吗？此操作不可恢复。',
    onOk() {
      tableData.value = [];
      message.success('数据已清空');
    },
  });
};

// 编辑记录
const editRecord = (record: any) => {
  message.info(`编辑用户：${record.name}`);
  // 这里可以打开编辑弹窗
};

// 删除记录
const deleteRecord = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除用户"${record.name}"吗？`,
    onOk() {
      const index = tableData.value.findIndex(item => item.key === record.key);
      if (index > -1) {
        tableData.value.splice(index, 1);
        // 重新计算序号
        tableData.value.forEach((item, idx) => {
          item.index = idx + 1;
        });
        message.success('删除成功');
      }
    },
  });
};

// 数据验证函数
const validateImportData = (data: any[]): ValidationResult => {
  const errors: string[] = [];
  
  if (data.length === 0) {
    errors.push('没有可导入的数据');
    return { valid: false, errors };
  }

  data.forEach((row, index) => {
    const rowNum = index + 1;
    
    // 检查必填字段
    if (!row[0] || String(row[0]).trim() === '') {
      errors.push(`第${rowNum}行：姓名不能为空`);
    }
    
    if (!row[1] || String(row[1]).trim() === '') {
      errors.push(`第${rowNum}行：邮箱不能为空`);
    } else {
      // 邮箱格式验证
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(String(row[1]).trim())) {
        errors.push(`第${rowNum}行：邮箱格式不正确`);
      }
    }
    
    // 检查电话格式
    if (row[2] && String(row[2]).trim()) {
      const phoneRegex = /^1[3-9]\d{9}$/;
      const phone = String(row[2]).trim().replace(/\D/g, '');
      if (!phoneRegex.test(phone)) {
        errors.push(`第${rowNum}行：电话格式不正确（应为11位手机号）`);
      }
    }

    // 检查重复邮箱
    const email = String(row[1]).trim();
    const existingUser = tableData.value.find(user => user.email === email);
    if (existingUser) {
      errors.push(`第${rowNum}行：邮箱"${email}"已存在`);
    }
  });
  
  return {
    valid: errors.length === 0,
    errors,
  };
};

// 数据转换函数
const transformImportData = (data: any[]) => {
  return data.map((row, index) => ({
    key: `import_${Date.now()}_${index}`,
    index: tableData.value.length + index + 1,
    name: String(row[0] || '').trim(),
    email: String(row[1] || '').trim(),
    phone: String(row[2] || '').trim(),
    department: String(row[3] || '').trim() || '未分配',
    position: String(row[4] || '').trim() || '员工',
    status: '激活',
    importTime: new Date().toLocaleString('zh-CN'),
  }));
};

// 导入成功回调
const handleImportSuccess = (data: any[]) => {
  console.log('导入成功:', data);
  
  // 将数据添加到表格
  tableData.value = [...tableData.value, ...data];
  
  message.success(`成功导入 ${data.length} 条用户数据`);
};

// 导入失败回调
const handleImportError = (error: string) => {
  console.error('导入失败:', error);
  message.error(`导入失败: ${error}`);
};
</script>

<style scoped>
.excel-import-example {
  padding: 0;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.empty-state p {
  margin: 16px 0;
  font-size: 14px;
}

/* 表格样式优化 */
:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}

:deep(.ant-statistic-content-value) {
  font-size: 24px;
}
</style>
